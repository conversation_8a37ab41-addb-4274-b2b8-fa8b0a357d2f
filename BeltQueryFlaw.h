#ifndef BELTQUERYFLAW_H
#define BELTQUERYFLAW_H

//(*Headers(BeltQueryFlaw)
#include <wx/bmpbuttn.h>
#include <wx/choice.h>
#include <wx/dialog.h>
#include <wx/gauge.h>
#include <wx/grid.h>
#include <wx/statbox.h>
#include <wx/stattext.h>
#include <wx/treectrl.h>
//*)
#include <wx/panel.h>
#include <wx/scrolwin.h>
#include <wx/statbmp.h>
#include "yblib.h"
#include <string>
#include <list>
#include <map>
#include "BeltChart.h"
#include "tcp_client_srcdata.h"
#include "wxCurve.h"
#include "analogChart.h"

class FlawData
{
  public:
	float pos;
	int ss;
	int ee;
	float value;
	float valEx;
	int level;
	char jtnum[16];
	float refpos;
	int rate;
	int alarm;
	bool sign;
	bool operator < (const FlawData& d)
	{
		return pos < d.pos;
	}
	void operator = (const FlawData& d)
	{
		pos = d.pos;
		ss = d.ss;
		ee = d.ee;
		value = d.value;
		valEx = d.valEx;
		level = d.level;
		memcpy(jtnum, d.jtnum, 16);
		refpos = d.refpos;
		rate = d.rate;
		alarm = d.alarm;
		sign = d.sign;
	}
};

class BeltQueryFlaw: public wxDialog, public ThreadBase
{
	public:

		BeltQueryFlaw(wxWindow* parent, bool isReg, const char *sn, const char *node, float sstep, int ropeCnt, int ssCnt, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltQueryFlaw();

		void SetJtData(Redis& jdata);
		void SetData(Redis& jdata, int beltWidth);
		void SetAlarmLog(Redis& jdata);
		void FlawMarkReply(int reply);

		void ThreadFuncDrawCurve(void *, unsigned int);

		//(*Declarations(BeltQueryFlaw)
		wxBitmapButton* BitmapButton1;
		wxBitmapButton* BitmapButton2;
		wxBitmapButton* BitmapButton3;
		wxBitmapButton* BitmapButton4;
		wxChoice* Choice1;
		wxGauge* Gauge1;
		wxGrid* Grid1;
		wxStaticBox* StaticBox1;
		wxStaticBox* StaticBox2;
		wxStaticText* StaticText1;
		wxStaticText* StaticText2;
		wxStaticText* StaticText3;
		wxStaticText* StaticText4;
		wxTreeCtrl* TreeCtrl1;
		//*)

		std::vector<AnalogJtData> m_jt_data;

		wxCurve *m_curve;
		float m_sstep;

	protected:

		void OnSrcDataProgress(wxCommandEvent& event);

		//(*Identifiers(BeltQueryFlaw)
		static const long ID_TREECTRL1;
		static const long ID_GRID1;
		static const long ID_STATICBOX1;
		static const long ID_STATICBOX2;
		static const long ID_STATICTEXT1;
		static const long ID_STATICTEXT2;
		static const long ID_STATICTEXT3;
		static const long ID_STATICTEXT4;
		static const long ID_CHOICE1;
		static const long ID_GAUGE1;
		static const long ID_BITMAPBUTTON1;
		static const long ID_BITMAPBUTTON2;
		static const long ID_BITMAPBUTTON3;
		static const long ID_BITMAPBUTTON4;
		//*)

	private:

		wxTreeItemId m_treeRoot;
		std::string m_sn;
		std::string m_node;
		bool m_isReg;

		BeltChart *m_chart_flawLev;

		int m_query_flag;

		void QueryData(wxString date);

		std::list<FlawData> m_flaw_data;
		int m_beltWidth;

		TcpClientSrcdata *m_tcp_srcdata;
		std::map<wxString, wxString> m_obtained_files;
		wxString m_srcdata_date;
		wxString m_srcdata_fname;

		int m_mark_row;

		int m_ropeCnt;
		int m_ssCnt;
		AnalogChart *m_analogChart;

		//(*Handlers(BeltQueryFlaw)
		void OnTreeCtrl1SelectionChanged(wxTreeEvent& event);
		void OnChoice1Select(wxCommandEvent& event);
		void OnBitmapButton1Click(wxCommandEvent& event);
		void OnBitmapButton2Click(wxCommandEvent& event);
		void OnBitmapButton3Click(wxCommandEvent& event);
		void OnBitmapButton4Click(wxCommandEvent& event);
		void OnGrid1CellLeftDClick(wxGridEvent& event);
		void OnClose(wxCloseEvent& event);
		//*)
		void OnGrid1Sort(wxGridEvent& event);

		DECLARE_EVENT_TABLE()
};

#endif
