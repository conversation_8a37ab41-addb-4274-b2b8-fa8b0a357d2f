#ifndef BELTYBREDOSELDATE_H
#define BELTYBREDOSELDATE_H

//(*Headers(BeltYBRedoSelDate)
#include <wx/button.h>
#include <wx/calctrl.h>
#include <wx/dialog.h>
#include <wx/stattext.h>
//*)

class BeltYBRedoSelDate: public wxDialog
{
	public:

		BeltYBRedoSelDate(wxWindow* parent,wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltYBRedoSelDate();

		wxString GetSelDate();

		//(*Declarations(BeltYBRedoSelDate)
		wxButton* Button1;
		wxCalendarCtrl* CalendarCtrl1;
		wxStaticText* StaticText1;
		//*)

	protected:

		//(*Identifiers(BeltYBRedoSelDate)
		static const long ID_STATICTEXT1;
		static const long ID_CALENDARCTRL1;
		static const long ID_BUTTON1;
		//*)

	private:

		//(*Handlers(BeltYBRedoSelDate)
		void OnButton1Click(wxCommandEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
