#ifndef TRANSSTATICTEXT_H
#define TRANSSTATICTEXT_H

#include <wx/window.h>
#include <wx/dcmemory.h>
#include <wx/bitmap.h>

class TransStaticText : public wxWindow
{
    public:
        TransStaticText(wxWindow *parent, const wxString &label, const wxFont &labelFont, const wxColour &labelColor,
						 const wxPoint &pos=wxDefaultPosition, const wxSize &size=wxDefaultSize, wxBitmap *pbgBmp=NULL);
        virtual ~TransStaticText();
        void SetLabelText(const wxString &label);
        const wxString& GetLabelText();
        void SetLabelFont(const wxFont &labelFont);
        void SetPosition( const wxPoint &  pt );
        void SetSize( const wxSize &  size );
    private:
        void OnPaint(wxPaintEvent &event);
        void OnEraseBackground(wxEraseEvent &event);
    private:
        wxWindow *m_parent;
        wxMemoryDC m_mdc;
        wxBitmap *m_pbgBmp;
        wxSize m_srcSize;
        wxSize m_size;
        wxPoint m_pos;
        wxString m_label;
        wxFont m_srcFont;
        wxFont m_labelFont;
        wxColour m_labelColor;
};

#endif // TRANSSTATICTEXT_H
