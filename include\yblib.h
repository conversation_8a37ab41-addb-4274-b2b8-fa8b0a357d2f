#ifndef _YB_LIB_H_
#define _YB_LIB_H_

#ifdef WIN32
#ifdef BUILD_DLL
    #define DLL_EXPORT __declspec(dllexport)
#else
    #define DLL_EXPORT __declspec(dllimport)
#endif
#else
    #define DLL_EXPORT
#endif

#ifdef _MSC_VER
	#define PACKED
	#pragma pack(1) // 将对齐模数设置为 1
#else
	#define PACKED __attribute__((__packed__))
#endif

#ifdef __x86_64__
    #define YBPID long long
#else
    #define YBPID int
#endif

/* --------------------------------------------------------------------- */

/* 函数说明
  1. Debug函数，打印输出
     Debug(f, s, ...); 输出至文件f中
	 Debug(0, s, ...); 输出至stdout，若调用DebugFile指定输出文件，则输出至此文件
  2. DebugFile函数，指定输出文件
*/
extern void DLL_EXPORT Debug(void *stream, const char* format, ...);
extern void DLL_EXPORT DebugType(int type);
extern void DLL_EXPORT DebugFile(const char *filename);

///Debug接口类
class DLL_EXPORT DebugInterface
{
  public:
	DebugInterface(){};
    virtual ~DebugInterface(){};
	/* 函数说明：Debug信息接口 */
	virtual void DebugInfo(const char *dbgstr){}
};
void DLL_EXPORT DebugIFS(DebugInterface *obj, char isAdd);


/* --------------------------------------------------------------------- */

//智能指针
template <class T>
class DLL_EXPORT SmartPtr
{
  public:
	SmartPtr(YBPID MPID=0, T *p = 0, unsigned int l=0);
	SmartPtr(const SmartPtr& sp);
	~SmartPtr();
	SmartPtr& operator =(const SmartPtr& sp);
	T* operator ->();
	const T* operator ->() const;
	T& operator *();
	const T& operator *() const;
	int GetCnt();
	void newSP(YBPID MPID, T *p, unsigned int l=0);
	void delSP();
	T* getPtr() const;
	unsigned int getLen() const;
	void setLen(unsigned int l);
  private:
	YBPID MemPoolID;
	T *ptr;
	unsigned int len;
	unsigned long *cnt;
};

//创建内存池
/*
  函数参数：
	blockCount: 申请块数量
	blockSize: 块字节大小
	blockStep: 块数增长步长
  返回值：内存池ID
*/
extern YBPID DLL_EXPORT MemPool_init(unsigned int blockCount, unsigned int blockSize, unsigned int blockStep);

//释放内存池
/*
	内存池不用了要记得释放哦
*/
extern int DLL_EXPORT MemPool_release(YBPID MemPoolID);

//从内存池中分配内存
extern char * DLL_EXPORT MemPool_alloc(YBPID MemPoolID);

//回收内存至内存池
extern int DLL_EXPORT MemPool_free(YBPID MemPoolID, const char *ptr);

//从内存池中分配内存（不需手动回收）
extern SmartPtr<char> DLL_EXPORT MemPool_GetSP(YBPID MemPoolID);


/* --------------------------------------------------------------------- */

//函数指针
typedef void (*ThreadFunc)(void *, unsigned int);
typedef void (*ThreadFuncSP)(SmartPtr<char>& sp);

class ThreadBase;
//函数指针（类）
typedef void (ThreadBase::*ThreadFunc_c)(void *, unsigned int);
typedef void (ThreadBase::*ThreadFuncSP_c)(SmartPtr<char>& sp);

//线程基类
class DLL_EXPORT ThreadBase
{
  public:
	ThreadBase(){};
	~ThreadBase(){};
	///创建一个线程，传入执行函数，执行完毕后线程结束（释放）
	/* 说明：主要在长期或永久性占用一个线程时用。如果仅是执行一个任务，请将任务放置线程池中执行 */
	static unsigned long CreateThread(ThreadFunc func, void *arg, unsigned int arglen, const char *threadName=0, int priority=0);
	static unsigned long CreateThread(ThreadBase *obj, ThreadFunc_c func, void *arg, unsigned int arglen, const char *threadName=0, int priority=0);
};

//创建线程池
/* 函数说明：只能在主线程中调用哦 */
extern int DLL_EXPORT ThreadPool_create(int maxthreadcount, int priority);

//添加任务
/* 函数说明：若arg非线程安全，或为局部变量，请使用内存池（传入内存池ID参数）分配内存（不需手动释放）*/
///使用C函数执行
extern int DLL_EXPORT ThreadPool_AddWorker(ThreadFunc func, void *arg, unsigned int arglen, YBPID MemPoolID, const char *threadName=0);
extern int DLL_EXPORT ThreadPool_AddWorker(ThreadFuncSP func, const SmartPtr<char>& sp, const char *threadName=0);
///使用类成员函数执行（类需派生于线程基类ThreadBase）
extern int DLL_EXPORT ThreadPool_AddWorker_c(ThreadBase *obj, ThreadFunc_c func, void *arg, unsigned int arglen, YBPID MemPoolID, const char *threadName=0);
extern int DLL_EXPORT ThreadPool_AddWorker_c(ThreadBase *obj, ThreadFuncSP_c func, const SmartPtr<char>& sp, const char *threadName=0);

//清空任务
extern void DLL_EXPORT ThreadPool_clearWorker();


/* --------------------------------------------------------------------- */

//TCP基类
class DLL_EXPORT TcpBase : public ThreadBase
{
	friend void ThreadRun(void *arg, unsigned int arglen);
	friend void ObjFree(TcpBase *obj);

  public:
	TcpBase();
	virtual ~TcpBase();

  public:
	/*函数说明：创建一个TCP服务端，timeout参数指定连接超时时间（单位：秒，设置为0永不超时），revBufLen设置接收数据Buffer长度 */
	int CreateServer(unsigned short port, unsigned int timeout, unsigned short revBufLen);
	/*函数说明：创建一个TCP客户端，timeout参数指定连接超时时间（单位：秒，设置为0永不超时），revBufLen设置接收数据Buffer长度 */
	int CreateClient(unsigned int timeout, unsigned short revBufLen);
	/*函数说明：TCP客户端，发起一个连接，返回连接标识符fd。参数waittime为连接等待超时时间，单位ms */
	YBPID ClientConnect(const char *ip, unsigned short port, int waittime = 200);
	/*函数说明：查询标示符fd对应的TCP连接的连接状态 */
	int CheckConnect(YBPID fd);
	/*函数说明：fd=0，关闭TCP服务端/TCP客户端/UDP/COM；fd=标示符，则关闭一个TCP连接 */
	int CloseTU(YBPID fd);
	/*函数说明：TCP发送数据。若buf非线程安全，或为局部变量，请使用内存池（传入内存池ID参数）分配内存（不需手动释放） */
	int Send(YBPID fd, const char *buf, unsigned int buflen, YBPID MemPoolID);
	int Send(YBPID fd, const SmartPtr<char>& sp);

	/*函数说明：创建一个UDP，revBufLen设置接收数据Buffer长度 */
	// bindIP可通过网卡IP绑定网卡，发送广播特别用到
	int CreateUDP(unsigned short port, unsigned short revBufLen, const char *bindIP = 0);
	/*函数说明：UDP发送数据。若buf非线程安全，或为局部变量，请使用内存池（传入内存池ID参数）分配内存（不需手动释放） */
	int SendTo(const char *ip, unsigned short port, const char *buf, unsigned int buflen, YBPID MemPoolID);
	int SendTo(const char *ip, unsigned short port, const SmartPtr<char>& sp);

	/*函数说明：创建一个COM串口连接，baudrate波特率，parity奇偶校验，stopbit停止位，revBufLen接收数据Buffer长度 */
	int CreateCOM(unsigned short port, unsigned int baudrate, char parity, char stopbit, unsigned short revBufLen);
	/*函数说明：COM发送数据。若buf非线程安全，或为局部变量，请使用内存池（传入内存池ID参数）分配内存（不需手动释放） */
	int SendCom(const char *buf, unsigned int buflen, YBPID MemPoolID);
	int SendCom(const SmartPtr<char>& sp);

	///打印发送队列长度信息
	static int PrintSendListInfo(char *msg);

  protected:
	/*函数说明：回调--TCP服务端，有连接请求。返回0拒绝，返回1同意 */
	virtual int Accepted(YBPID fd, const char *ip){return 1;}

	/*函数说明：回调--TCP/COM收到数据 */
	virtual void Received(YBPID fd, const char *buf, unsigned int buflen){}
	/*函数说明：回调--TCP/COM数据发送完毕。sdlen为成功发送字节数 */
	virtual void SendResult(YBPID fd, int sdlen){}

	/*函数说明：回调--TCP/UDP/COM连接断开，主动调用CloseServer、CloseClient、CloseConnect、CloseTU不会回调此函数 */
	virtual void Disconnected(YBPID fd){}

	/*函数说明：回调--UDP收到数据 */
	virtual void ReceiveFrom(const char *ip, unsigned short port, const char *buf, unsigned int buflen){}
	/*函数说明：回调--UDP数据发送完毕。sdlen为成功发送字节数 */
	virtual void SendToResult(const char *ip, unsigned short port, int sdlen){}

  private:
	YBPID m_ID;
};


/* --------------------------------------------------------------------- */

//函数指针
typedef void (*TimerFunc)();

class TimerBase;
//函数指针（类）
typedef void (TimerBase::*TimerFunc_c)();

//定时器基类
class DLL_EXPORT TimerBase
{
  public:
	TimerBase(){};
	~TimerBase(){};

	///创建定时器（百毫秒级）
	/* interval 为时间间隔，单位：百毫秒，返回定时器ID */
	static YBPID CreateTimer(unsigned int interval, TimerFunc func);
	static YBPID CreateTimer(unsigned int interval, TimerFunc_c func, TimerBase *obj);
	///删除定时器
	static int DeleteTimer(YBPID ID);
	///启动定时器
	/* cnt 为定时执行次数，0值表示永久执行 */
	static int StartTimer(YBPID ID, unsigned int cnt);
	///停止定时器
	static int StopTimer(YBPID ID);

	///定时器运行（仅调用一次即可哦）
	/* 可指定useThread=true来启动线程执行定时器，也可手动阻塞来执行 */
	static int TimerRun(bool useThread = false);
};


/* --------------------------------------------------------------------- */

//函数指针
typedef void (*EventFunc)(void *, unsigned int);
typedef void (*EventFuncSP)(SmartPtr<char>& sp);

class EventBase;
//函数指针（类）
typedef void (EventBase::*EventFunc_c)(void *, unsigned int);
typedef void (EventBase::*EventFuncSP_c)(SmartPtr<char>& sp);

//消息事件基类
class DLL_EXPORT EventBase
{
  public:
	EventBase(){};
	~EventBase(){};

	///绑定事件
	/* 绑定可指定发起源obj_src，若指定，则仅处理来自此源端发起的事件 */
	/* 返回值：0=绑定失败，1=绑定成功，2=已绑定 */
	static int EvtBind(unsigned int ID, unsigned int evt, EventBase *obj_src, EventFunc func, EventFuncSP funcSP);
	static int EvtBind(unsigned int ID, unsigned int evt, EventBase *obj_src, EventFunc_c func, EventFuncSP_c funcSP, EventBase *obj_dst);
	///解绑事件
	/* 返回值：0=解绑失败，1=解绑成功，2=已解绑 */
	static int EvtUnBind(unsigned int ID, unsigned int evt, EventBase *obj_src, EventFunc func, EventFuncSP funcSP);
	static int EvtUnBind(unsigned int ID, unsigned int evt, EventBase *obj_src, EventFunc_c func, EventFuncSP_c funcSP, EventBase *obj_dst);
	///发送事件
	/* 发送事件可标明来源，若标明，则绑定发起源的，必须匹配 */
	static int EvtSend(unsigned int ID, unsigned int evt, EventBase *obj_src, EventBase *obj_dst, void *arg, unsigned int arglen, YBPID MemPoolID);
	///发送事件（使用智能指针）
	static int EvtSend(unsigned int ID, unsigned int evt, EventBase *obj_src, EventBase *obj_dst, const SmartPtr<char>& sp);

	///打印消息队列长度信息
	static int PrintEvtListInfo(char *msg, const char **nameID, const char **nameEvt);
};

/* 消息事件使用说明
	1. 在使用绑定发起源obj_src时，需要注意在类定义中，继承EventBase消息事件基类，要放在第一个
		例如：class wxCurve : public EventBase, public wxPanel  不要写成  class wxCurve : public wxPanel, public EventBase
*/

//消息事件ID定义
enum EventID
{
    EvtID_Error,		///错误事件
    EvtID_wxCurve,		///曲线图
	EvtID_SlaveArm7,	///二级站Arm7
	EvtID_SpiGpio,		///SPI驱动站
	EvtID_AlgoJT,		///接头算法
	EvtID_Algo,			///损伤算法
	EvtID_Station,		///检测站点
	EvtID_Register,		///注册服务（UDP）
	EvtID_Inpos,		///到位信号
	EvtID_SMS,			///短信
	EvtID_Report,		///检测报告
	EvtID_Button,		///按钮
    EvtID_Count			///数量
};

//错误事件定义
enum
{
	Evt_Error_Unknow,		///未知错误
	Evt_Error_Fatal,		///致命错误
	Evt_Error_Warning		///警告
};


/* --------------------------------------------------------------------- */

//JSON类
class DLL_EXPORT Json
{
  public:
	/* JSON Types */
	enum
	{
		JSON_TYPE_IDLE = 0,
		JSON_TYPE_False = 1,
		JSON_TYPE_True = 2,
		JSON_TYPE_NULL = 3,
		JSON_TYPE_Int = 4,
		JSON_TYPE_Number = 5,
		JSON_TYPE_String = 6,
		JSON_TYPE_Array = 7,
		JSON_TYPE_Object = 8
	} JsonType;

  public:
	Json();
	~Json();
	Json(const char *str); ///构造并解析json字符串str
	Json(const Json& json); ///拷贝构造函数
	Json& operator =(const Json& json); ///赋值操作符
	void operator =(const bool& val);
	void operator =(const int& val);
	void operator =(const float& val);
	void operator =(const double& val);
	void operator =(const char *val);
	//int operator ()(void *val, int len=0); ///取值用()操作符
	void operator ()(bool& val); ///取值用()操作符--bool
	void operator ()(int& val); ///取值用()操作符--int
	void operator ()(float& val); ///取值用()操作符--float
	void operator ()(double& val); ///取值用()操作符--double
	int operator ()(char *val, int len); ///取值用()操作符--string
	Json& GetValue(bool& val);
	Json& GetValue(int& val);
	Json& GetValue(float& val);
	Json& GetValue(double& val);
	Json& GetValue(char *val, int len);
	Json& operator [](int idx); ///数组用[]操作符
	Json& operator [](const char *key); ///对象用[]操作符
	int ParseJson(const char *str); ///解析json字符串
	int PrintJson(char *str, int len); ///将json数据转为字符串
	bool AddItem(int type, const char *name, double val, const char *str); ///添加元素
	Json& AddItem(int type, const char *name, double val, const char *str, bool isContinueEnd);
	bool Del();	///删除元素

  private:
	void *m_jsonData;
	unsigned long *m_cnt;
	void *m_tmpPt;

	void DelJsonData(); ///删除json数据
	int ParseJsonData(const char *str); ///解析json字符串
};

//Redis类
class DLL_EXPORT Redis
{
  public:
	/* Redis Types */
	enum
	{
		REDIS_TYPE_IDLE = 0,
		REDIS_TYPE_INT = 1,
		REDIS_TYPE_NUMBER = 2,
		REDIS_TYPE_STRING = 3,
		REDIS_TYPE_ERROR = 4,
		REDIS_TYPE_STATUS = 5,
		REDIS_TYPE_ARRAY = 6,
		REDIS_TYPE_OBJECT = 7,
		REDIS_TYPE_NULL = 8,
	} RedisType;

  public:
	Redis();
	~Redis();
	Redis(const Redis& redis); ///拷贝构造函数
	Redis& operator =(const Redis& redis); ///赋值操作符
	void operator =(const int val);
	void operator =(const long long val);
	void operator =(const float val);
	void operator =(const double val);
	void operator =(const char *val);
	void operator ()(int& val); ///取值用()操作符--int
	void operator ()(long long& val); ///取值用()操作符--long long
	void operator ()(float& val); ///取值用()操作符--float
	void operator ()(double& val); ///取值用()操作符--double
	int operator ()(char *val, unsigned int len); ///取值用()操作符--string
	Redis& GetValue(int& val);
	Redis& GetValue(long long& val);
	Redis& GetValue(float& val);
	Redis& GetValue(double& val);
	Redis& GetValue(char *val, unsigned int len);
	Redis& operator [](unsigned int idx); ///数组用[]操作符
	Redis& operator [](const char *key); ///对象用[]操作符
	bool AddItem(int type, const char *name, long long val, double val2, const char *str, unsigned int slen); ///添加元素
	Redis& AddItem(int type, const char *name, double val, const char *str, bool isContinueEnd);
	bool AddItem(Redis& Redis, bool isOnlyChild=false); ///将其它Redis添加过来，注意：使用剪切而非拷贝方式
	bool Del();	///删除元素
	int GetType();
	int GetSize();
	void *SaveTmpPt();
	Redis& RecoverTmpPt(void *tmpPt=0);
	Redis& ResetTmpPt();
	bool CheckName(const char *name);
	void SetName(const char *name);
	void ArrayToObject();

	int ParseRedis(const char *str, unsigned int len, bool& isgoon); ///解析redis字符串
	int PrintRedis(char *str, unsigned int len, bool& isgoon); ///将数据转为redis字符串

	int ParseJson(const char *str);	///解析Json字符串
	int PrintJson(char *str, unsigned int len, bool isUTF8Serialize=true); ///将数据转为json字符串
	int ParseJson(const char *str, bool& isgoon);
	int PrintJson(char *str, unsigned int len, bool& isgoon, bool isUTF8Serialize=true);

	int ParseXML(const char *str, bool& isgoon, bool isMulti=false);
	int PrintXML(char *str, unsigned int len, bool& isgoon);
	///由于xml存在<?xml ?>头时会创建xml根节点，但是xml文档本身没有xml成对的</xml>
	///因此当ParseXML的isMulti参数为true时，需要手动调用此函数来终结本次ParseXML，示意XML文件已结束
	void ParseXMLOver();
	///此函数用于将对象中标识名相同的元素合并成为一个数组元素，用于XML特性
	void MergeSameNameElementsToArray();

	///修改临时用全局打印浮点数精度
	static void SetPrintFloatPrecision(int prec);

  private:
	void *m_redisData;
	unsigned long *m_cnt;
	void *m_tmpPt;
	void *m_pt;
	void *m_tmpPt_bk;

	void DelRedisData(); ///删除redis数据

	int ParseRedisData(const char *str, unsigned int len, bool& isgoon); ///解析redis字符串

	int ParseJsonData(const char *str); ///解析json字符串
	int ParseJsonData(const char *str, bool& isgoon);

	int ParseXMLData(const char *str, bool& isgoon, bool isMulti);
};

//Redis加密 -- key有效字节数 = 8
extern void DLL_EXPORT SetDesKey(const char *key);
extern bool DLL_EXPORT MsgDecrypt(Redis& json, char *str, int len);
extern int DLL_EXPORT MsgEncrypt(Redis& json, char *str, int len);
//DES加密
extern int DLL_EXPORT DES_Encrypt(char *srcStr, unsigned int srcLen, char *keyStr, char *dstStr);
extern int DLL_EXPORT DES_Decrypt(char *srcStr, unsigned int srcLen, char *keyStr, char *dstStr);
//DES补码字符
extern void DLL_EXPORT DES_Set_PatchingChar(char c);
//Base64转码
extern int DLL_EXPORT Base64_Encode(unsigned char* src, unsigned int srclen, char* dest);
extern int DLL_EXPORT Base64_Decode(unsigned char* src, unsigned int srclen, char* dest);


/* --------------------------------------------------------------------- */

//MD5
extern "C" void DLL_EXPORT md5_passwd(char *oldpasswd, char *md5_passwd);
extern "C" void DLL_EXPORT MD5_Binary(char *srcbuf, int srclen, char *dstbuf);
#define MD5 md5_passwd


/* --------------------------------------------------------------------- */

//宏定义（公用）

//最大传感器数量
#define MAX_SENSOR_COUNT 96

//互联网MTU=576（576-8-20=548）
#define MTU_INT_LEN 512

//局域网MTU=1472
#define MTU_LAN_LEN 1440


#endif
