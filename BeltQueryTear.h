#ifndef BELTQUERYTEAR_H
#define BELTQUERYTEAR_H

//(*Headers(BeltQueryTear)
#include <wx/button.h>
#include <wx/dialog.h>
#include <wx/gauge.h>
#include <wx/grid.h>
#include <wx/scrolwin.h>
#include <wx/stattext.h>
//*)
#include <vector>
#include "yblib.h"
#include "tcp_client_web.h"
#include "tcp_client_srcdata.h"
#include "BeltTearReport.h"

class BeltQueryTear: public wxDialog
{
	public:

		BeltQueryTear(wxWindow* parent, const char *sn, const char *node, const TcpClientWeb::DataEvtReplyRope *rope_info, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltQueryTear();

		void SetData(Redis& jdata);

		//(*Declarations(BeltQueryTear)
		wxButton* Button1;
		wxGauge* Gauge1;
		wxGrid* Grid1;
		wxScrolledWindow* ScrolledWindow1;
		wxStaticText* StaticText1;
		//*)

	protected:

		void OnTearPhotoProgress(wxCommandEvent& event);
		void OnPhotoLeftDClick(wxMouseEvent& event);

		//(*Identifiers(BeltQueryTear)
		static const long ID_GRID1;
		static const long ID_SCROLLEDWINDOW1;
		static const long ID_STATICTEXT1;
		static const long ID_BUTTON1;
		static const long ID_GAUGE1;
		//*)

	private:

		wxWindow *m_parent;
		std::string m_sn;
		std::string m_node;
		const TcpClientWeb::DataEvtReplyRope *m_rope_info;

		BeltTearReport *m_report_dlg;
		std::vector<int> m_tear_ids;
		TcpClientSrcdata *m_tcp_srcdata;

		void AddPhoto(const wxString& fname, int fmt);

		wxPanel *m_photos_panel;

		//(*Handlers(BeltQueryTear)
		void OnGrid1CellLeftDClick(wxGridEvent& event);
		void OnButton1Click(wxCommandEvent& event);
		void OnClose(wxCloseEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
