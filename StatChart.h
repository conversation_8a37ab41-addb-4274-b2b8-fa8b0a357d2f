#ifndef STATCHART_H
#define STATCHART_H

#include <wx/panel.h>
#include <wx/bitmap.h>
#include <wx/dcmemory.h>
#include <vector>

typedef struct
{
	wxDateTime date;
	float value;
} StatData;

class StatChart: public wxPanel
{
	public:
		StatChart(wxWindow* parent, int type, wxString yUnit, const wxColour *color=0, int fontSize=8, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~StatChart();
        void SetData(const std::vector<StatData>& data, float minY, float maxY);
		wxBitmap GetBitmap(short angle);

	private:
		wxWindow *m_parent;
		wxBitmap m_firstBgBmp;
        wxFont m_font;
        wxColour m_color;
        int m_fontSize;
        wxBitmap m_bmp;

		int m_type;
		wxString m_yUnit;

		void OnEraseBackground(wxEraseEvent& event);
		void OnPaint(wxPaintEvent& event);

		DECLARE_EVENT_TABLE()
};

#endif
