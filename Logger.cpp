#include "Logger.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <chrono>

// 全局日志实例定义
SimpleLogger g_logger;

std::string SimpleLogger::GetCurrentTime() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::stringstream ss;
    ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

bool SimpleLogger::Initialize(const std::string& logFilePath) {
    std::lock_guard<std::mutex> lock(logMutex);

    if (fileLoggingEnabled) {
        logFile.open(logFilePath, std::ios::app);
        if (!logFile.is_open()) {
            std::cout << "警告: 无法打开日志文件 " << logFilePath << std::endl;
            fileLoggingEnabled = false;
            return false;
        }

        // 写入启动标记
        logFile << "\n========== logger system start " << GetCurrentTime() << " ==========" << std::endl;
        logFile.flush();
    }

    return true;
}

void SimpleLogger::Log(const std::string& level, const std::string& msg) {
    std::lock_guard<std::mutex> lock(logMutex);

    std::string timestamp = GetCurrentTime();
    std::string logEntry = "[" + timestamp + "] [" + level + "] " + msg;

    // 输出到控制台
    if (consoleLoggingEnabled) {
        std::cout << logEntry << std::endl;
    }

    // 写入日志文件
    if (fileLoggingEnabled && logFile.is_open()) {
        logFile << logEntry << std::endl;
        logFile.flush();
    }
}

void SimpleLogger::LogInfo(const std::string& msg) {
    Log("INFO", msg);
}

void SimpleLogger::LogError(const std::string& msg) {
    Log("ERROR", msg);
}

void SimpleLogger::LogWarning(const std::string& msg) {
    Log("WARNING", msg);
}

void SimpleLogger::LogDebug(const std::string& msg) {
    Log("DEBUG", msg);
}

void SimpleLogger::SetConsoleLogging(bool enabled) {
    consoleLoggingEnabled = enabled;
}

void SimpleLogger::SetFileLogging(bool enabled) {
    fileLoggingEnabled = enabled;
}

SimpleLogger::~SimpleLogger() {
    if (logFile.is_open()) {
        std::lock_guard<std::mutex> lock(logMutex);
        logFile << "========== logger system stop " << GetCurrentTime() << " ==========" << std::endl;
        logFile.close();
    }
}

// 简单的日志函数实现
void LogMessage(const std::string& msg) {
    g_logger.LogInfo(msg);
}

void LogError(const std::string& msg) {
    g_logger.LogError(msg);
}

void LogWarning(const std::string& msg) {
    g_logger.LogWarning(msg);
}
