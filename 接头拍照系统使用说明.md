# 接头自动拍照系统使用说明

## 1. 系统概述

接头自动拍照系统是在现有皮带输送带检测系统基础上增加的功能模块，能够在检测到接头时自动拍摄照片，并提供便捷的图片查看功能。

## 2. 功能特点

### 2.1 自动拍照
- **触发时机**: 当MRT设备检测到接头时自动触发拍照
- **时机计算**: 根据接头长度、皮带速度和摄像头安装位置智能计算最佳拍摄时机
- **高质量成像**: 支持400万像素分辨率，30fps实时图像输出

### 2.2 智能存储
- **目录结构**: 按日期和圈数自动创建目录 `/photos/YYYY-MM-DD/circle_N/`
- **文件命名**: 规范命名格式 `joint_[接头编号]_[时间戳].jpg`
- **自动管理**: 系统自动创建必要的目录结构

### 2.3 便捷查看
- **右键菜单**: 在接头查询页面右键点击接头编号即可查看照片
- **图片缩放**: 支持放大、缩小、重置等基本操作
- **信息显示**: 显示接头编号、图片尺寸、文件信息等

## 3. 安装配置

### 3.1 摄像头安装
1. **安装位置**: 摄像头应安装在MRT设备后方，距离设备约2米处
2. **安装高度**: 确保摄像头能够拍摄到完整的接头区域
3. **网络连接**: 摄像头通过网线连接到局域网，确保与检测系统在同一网段

### 3.2 配置文件设置
编辑 `capture_config.ini` 文件：

```ini
[Camera]
# 摄像头IP地址（根据实际情况修改）
IP=*************
Port=8000
Username=admin
Password=admin123
Resolution=2560x1440
FrameRate=30

[Capture]
# 摄像头安装距离(米)
CameraDistance=2.0
# 安全缓冲距离(米)
SafetyBuffer=0.5
# 照片质量(0-100)
PhotoQuality=95
# 是否启用自动拍照
AutoCaptureEnabled=true
# 照片存储根目录
PhotoRootPath=photos
```

### 3.3 摄像头距离计算

摄像头的最佳安装距离计算公式：
```
最佳距离 = 接头长度/2 + 安全缓冲距离
```

例如：
- 接头长度为2米，安全缓冲距离为0.5米
- 最佳安装距离 = 2/2 + 0.5 = 1.5米

## 4. 使用方法

### 4.1 自动拍照
1. 确保摄像头已正确连接并配置
2. 启动检测系统，系统会自动加载拍照配置
3. 当检测到接头时，系统会自动计算拍照时机并执行拍照
4. 拍摄的照片会自动保存到指定目录

### 4.2 查看接头照片
1. 打开"接头查询"页面
2. 在接头列表中找到要查看的接头
3. 右键点击接头编号
4. 选择"查看接头 XXX 的照片"菜单项
5. 系统会打开图片查看器显示该接头的最新照片

### 4.3 图片查看器操作
- **放大**: 点击"放大 (+)"按钮或使用鼠标滚轮
- **缩小**: 点击"缩小 (-)"按钮或使用鼠标滚轮
- **重置**: 点击"重置"按钮恢复原始大小
- **关闭**: 点击"关闭"按钮退出查看器

## 5. 故障排除

### 5.1 拍照失败
**现象**: 检测到接头但没有拍照
**可能原因**:
1. 摄像头网络连接异常
2. 摄像头IP地址配置错误
3. 用户名密码不正确
4. 自动拍照功能被禁用

**解决方法**:
1. 检查网络连接和IP配置
2. 验证摄像头登录信息
3. 确认配置文件中 `AutoCaptureEnabled=true`

### 5.2 照片查看失败
**现象**: 右键菜单显示"未找到照片"
**可能原因**:
1. 照片存储目录不存在
2. 文件权限问题
3. 照片文件被误删

**解决方法**:
1. 检查照片存储目录是否存在
2. 确认程序有读写权限
3. 查看系统日志了解详细错误信息

### 5.3 性能问题
**现象**: 拍照延迟过大或影响检测性能
**解决方法**:
1. 调整摄像头分辨率和帧率
2. 优化网络带宽
3. 检查系统资源使用情况

## 6. 维护建议

### 6.1 定期检查
- 每周检查摄像头连接状态
- 定期清理过期照片释放存储空间
- 检查配置文件是否被意外修改

### 6.2 备份管理
- 定期备份重要的接头照片
- 建立照片归档机制
- 制定存储空间管理策略

### 6.3 系统升级
- 关注海康威视SDK更新
- 定期更新系统补丁
- 备份配置文件后再进行升级

## 7. 技术支持

如遇到技术问题，请联系系统开发团队，并提供以下信息：
- 系统版本信息
- 错误日志文件
- 配置文件内容
- 问题复现步骤

