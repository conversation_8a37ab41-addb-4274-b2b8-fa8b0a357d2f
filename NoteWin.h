#ifndef NOTEWIN_H
#define NOTEWIN_H

//(*Headers(NoteWin)
#include <wx/stattext.h>
#include <wx/textctrl.h>
#include <wx/checkbox.h>
#include <wx/panel.h>
#include <wx/button.h>
#include <wx/dialog.h>
//*)

class NoteWin: public wxDialog
{
	public:

		NoteWin(wxWindow* parent,wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~NoteWin();

		//(*Declarations(NoteWin)
		wxButton* Button1;
		wxPanel* Panel1;
		wxStaticText* StaticText1;
		wxCheckBox* CheckBox1;
		wxTextCtrl* TextCtrl2;
		wxTextCtrl* TextCtrl1;
		//*)

	protected:

		//(*Identifiers(NoteWin)
		static const long ID_TEXTCTRL1;
		static const long ID_STATICTEXT1;
		static const long ID_PANEL1;
		static const long ID_CHECKBOX1;
		static const long ID_BUTTON1;
		static const long ID_TEXTCTRL2;
		//*)

	private:

		//(*Handlers(NoteWin)
		void OnCheckBox1Click(wxCommandEvent& event);
		void OnButton1Click(wxCommandEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
