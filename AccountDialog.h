#ifndef ACCOUNTDIALOG_H
#define ACCOUNTDIALOG_H

//(*Headers(AccountDialog)
#include <wx/button.h>
#include <wx/dialog.h>
#include <wx/grid.h>
#include <wx/stattext.h>
#include <wx/textctrl.h>
//*)
#include <string>
#include <map>
#include <vector>
#include "MainFrame.h"

class AccountDialog: public wxDialog
{
	public:

		AccountDialog(wxWindow* parent,wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~AccountDialog();

		void SetStationList(std::map<std::string, StationWin>& stationList);
		void SetAccountList(int reply, std::vector<TcpClientWeb::DataEvtAccount>& accountList);
		void SetRegisterReply(int reply);

		//(*Declarations(AccountDialog)
		wxButton* Button1;
		wxButton* Button2;
		wxButton* Button3;
		wxButton* Button4;
		wxGrid* Grid1;
		wxGrid* Grid2;
		wxStaticText* StaticText1;
		wxStaticText* StaticText2;
		wxStaticText* StaticText3;
		wxStaticText* StaticText4;
		wxStaticText* StaticText5;
		wxStaticText* StaticText6;
		wxStaticText* StaticText7;
		wxStaticText* StaticText8;
		wxTextCtrl* TextCtrl1;
		wxTextCtrl* TextCtrl2;
		wxTextCtrl* TextCtrl3;
		wxTextCtrl* TextCtrl4;
		wxTextCtrl* TextCtrl5;
		wxTextCtrl* TextCtrl6;
		//*)

	protected:

		//(*Identifiers(AccountDialog)
		static const long ID_STATICTEXT1;
		static const long ID_GRID1;
		static const long ID_STATICTEXT2;
		static const long ID_GRID2;
		static const long ID_STATICTEXT3;
		static const long ID_BUTTON1;
		static const long ID_TEXTCTRL1;
		static const long ID_STATICTEXT4;
		static const long ID_TEXTCTRL2;
		static const long ID_BUTTON2;
		static const long ID_STATICTEXT5;
		static const long ID_TEXTCTRL3;
		static const long ID_STATICTEXT6;
		static const long ID_TEXTCTRL4;
		static const long ID_STATICTEXT7;
		static const long ID_TEXTCTRL5;
		static const long ID_BUTTON3;
		static const long ID_BUTTON4;
		static const long ID_STATICTEXT8;
		static const long ID_TEXTCTRL6;
		//*)

	private:

		int m_modify_idx;
		TcpClientWeb::DataEvtAccount m_account_data;
		TcpClientWeb::DataEvtRegister m_register_data;

		//(*Handlers(AccountDialog)
		void OnButton3Click(wxCommandEvent& event);
		void OnButton4Click(wxCommandEvent& event);
		void OnButton1Click(wxCommandEvent& event);
		void OnButton2Click(wxCommandEvent& event);
		void OnGrid1CellLeftDClick(wxGridEvent& event);
		void OnGrid2CellLeftDClick(wxGridEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
