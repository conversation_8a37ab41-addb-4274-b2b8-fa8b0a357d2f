#ifndef ZXQUERYALARM_H
#define ZXQUERYALARM_H

//(*Headers(ZXQueryAlarm)
#include <wx/button.h>
#include <wx/dialog.h>
#include <wx/gauge.h>
#include <wx/grid.h>
#include <wx/stattext.h>
//*)
#include "yblib.h"
#include <string>
#include <map>

class ZXQueryAlarm: public wxDialog
{
	public:

		ZXQueryAlarm(wxWindow* parent, const char *sn, const char *node, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~ZXQueryAlarm();

		void SetData(Redis& jdata);
		void ResetReply(int reply);

		//(*Declarations(ZXQueryAlarm)
		wxButton* Button1;
		wxGauge* Gauge1;
		wxGrid* Grid1;
		wxGrid* Grid3;
		wxStaticText* StaticText1;
		wxStaticText* StaticText3;
		//*)

	protected:

		//(*Identifiers(ZXQueryAlarm)
		static const long ID_GRID1;
		static const long ID_GRID3;
		static const long ID_STATICTEXT1;
		static const long ID_STATICTEXT3;
		static const long ID_GAUGE1;
		static const long ID_BUTTON1;
		//*)

	private:

		std::string m_sn;
		std::string m_node;
		int m_data_flag;
		
		std::map<std::string, float>::iterator m_iterfcp;

		//(*Handlers(ZXQueryAlarm)
		void OnButton1Click(wxCommandEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
