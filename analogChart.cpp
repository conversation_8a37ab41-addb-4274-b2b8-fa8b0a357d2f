#include "analogChart.h"

#include <wx/intl.h>
#include <wx/string.h>
#include <wx/dcclient.h>
#include <sys/time.h>
#include <unistd.h>
#include <pthread.h>
#include <math.h>

AnalogChart::AnalogChart(wxWindow* parent,const wxPoint& pos,const wxSize& size)
{
	m_parent = parent;
	m_panel = NULL;
	if(parent)
	{
		m_panel = new wxPanel;
		m_panel->Create(parent, wxID_ANY, pos, size, wxTAB_TRAVERSAL);
		m_panel->Connect(wxEVT_PAINT,(wxObjectEventFunction)&AnalogChart::OnPaint, NULL, this);
		m_panel->Connect(wxEVT_MOUSEWHEEL,(wxObjectEventFunction)&AnalogChart::OnMouseWheel, NULL, this);
		m_panel->Connect(wxEVT_LEFT_DOWN,(wxObjectEventFunction)&AnalogChart::OnLeftDown, NULL, this);
		m_panel->Connect(wxEVT_LEFT_UP,(wxObjectEventFunction)&AnalogChart::OnLeftUp, NULL, this);
		m_panel->Connect(wxEVT_MOTION,(wxObjectEventFunction)&AnalogChart::OnMouseMove, NULL, this);
	}
	m_size = size;
    m_bmp.Create(size);
    m_dc.SelectObject(m_bmp);
    m_isInit = false;
	m_font = wxFont(8, wxSWISS,wxFONTSTYLE_NORMAL,wxBOLD, false, _("Arial"));
    m_dc.SetFont(m_font);
	m_ropeCnt = 0;
	m_sensorCnt = 0;
	m_leftOrRight = 0;
}

AnalogChart::~AnalogChart()
{
	if(m_panel)
	{
		m_panel->Destroy();
		m_panel = NULL;
	}
}

bool AnalogChart::IsInit()
{
	return m_isInit;
}

void AnalogChart::SetBG(const wxFont *font, const wxColour *bgColor, const wxColour *coColor, const wxColour *jtColor)
{
	if(font)
	{
		m_font = *font;
		m_dc.SetFont(m_font);
	}
	if(bgColor)
	{
		m_bgColor = *bgColor;
		m_dc.SetBackground(m_bgColor);
	}
	else if(m_parent)
	{
		m_bgBmp.Create(m_size);
		wxMemoryDC memDC;
		memDC.SelectObject(m_bgBmp);
        wxClientDC cdc(m_parent);
        memDC.Blit(0,0,m_size.GetWidth(),m_size.GetHeight(),&cdc,m_panel->GetPosition().x,m_panel->GetPosition().y,wxCOPY,false);
        memDC.SelectObject(wxNullBitmap);
	}
	else
	{
		m_bgColor = *wxWHITE;
		m_dc.SetBackground(m_bgColor);
	}
	if(coColor)
		m_coColor = *coColor;
	else
		m_coColor = *wxBLACK;
	if(jtColor)
		m_jtColor = *jtColor;
	else
		m_jtColor = *wxBLUE;
}

void AnalogChart::Initial(const wxBitmap& ele, const wxBitmap& brk, const wxBitmap& jt1, const wxBitmap& jt2, const wxBitmap& jt3, const wxBitmap& jt4, int ropeCnt, int sensorCnt, const std::vector<AnalogFlawData>& flaws, const std::vector<AnalogJtData>& jts)
{
	if((m_ropeCnt == 0 && ropeCnt == 0) || (m_sensorCnt == 0 && sensorCnt == 0) || (m_flaws.empty() && flaws.empty()) || (m_jts.empty() && jts.empty()))
		return;
	m_eleBmp = ele;
	m_brkBmp = brk;
	m_jtBmp[0] = jt1;
	m_jtBmp[1] = jt2;
	m_jtBmp[2] = jt3;
	m_jtBmp[3] = jt4;
	if(ropeCnt > 0)
		m_ropeCnt = ropeCnt;
	if(sensorCnt > 0)
		m_sensorCnt = sensorCnt;
	if(!flaws.empty())
		m_flaws = flaws;
	if(!jts.empty())
		m_jts = jts;
    m_isInit = true;
	m_spos = m_epos = 0;
	Initial();
}

void AnalogChart::Initial()
{
	if(m_bgBmp.IsOk())
		m_dc.DrawBitmap(m_bgBmp,0,0);
	else
		m_dc.Clear();
    if(m_parent)
	{
		//刷新
		m_panel->Refresh(false);
		//m_panel->Update();
	}
}

void AnalogChart::ReSize(const wxSize& size)
{
	if(size == m_size)
		return;
	if(m_parent)
		m_panel->SetSize(size.GetWidth(), size.GetHeight());
	m_dc.SelectObject(wxNullBitmap);
    m_bmp.Create(size);
    m_dc.SelectObject(m_bmp);
	m_size = size;
	Initial();
	DrawChart(m_spos, m_epos);
}

wxSize AnalogChart::GetSize()
{
	if(m_panel)
		return m_panel->GetSize();
	return wxSize(0,0);
}
void AnalogChart::Show()
{
	if(m_panel)
		m_panel->Show();
}
void AnalogChart::Hide()
{
	if(m_panel)
		m_panel->Hide();
}
wxPanel *AnalogChart::GetPanel()
{
	return m_panel;
}


wxBitmap AnalogChart::GetBitmap(short angle)
{
	wxBitmap bmp;
	wxMemoryDC memDC;
	wxAffineMatrix2D am2d;
	wxSize size;
	if(angle == 90)
	{
		size.SetWidth(m_size.GetHeight());
		size.SetHeight(m_size.GetWidth());
		///麻蛋的，wxWidgets-2.9.2是-1.5707964后Translate，wxWidgets-3.0.2是1.5707964先Translate …… 他大爷的，我实在不想说脏话的
#ifdef __x86_64__
		am2d.Translate(size.GetWidth(), 0);
		am2d.Rotate(1.5707964);
#else
		am2d.Rotate(-1.5707964);
		am2d.Translate(size.GetWidth(), 0);
#endif
	}
	else if(angle == -90)
	{
		size.SetWidth(m_size.GetHeight());
		size.SetHeight(m_size.GetWidth());
#ifdef __x86_64__
		am2d.Translate(0, size.GetHeight());
		am2d.Rotate(-1.5707964);
#else
		am2d.Rotate(1.5707964);
		am2d.Translate(0, size.GetHeight());
#endif
	}
	else if(angle == 180)
	{
		size = m_size;
#ifdef __x86_64__
		am2d.Translate(size.GetWidth(), size.GetHeight());
		am2d.Rotate(3.1415927);
#else
		am2d.Rotate(3.1415927);
		am2d.Translate(size.GetWidth(), size.GetHeight());
#endif
	}
	if(angle)
	{
		if(!memDC.SetTransformMatrix(am2d))
		{
			//Linux的wxDC不支持矩阵旋转
			bmp.Create(size);
			if(angle == 90)
				bmp = wxBitmap(m_bmp.ConvertToImage().Rotate90(true));
			else if(angle == -90)
				bmp = wxBitmap(m_bmp.ConvertToImage().Rotate90(false));
			else if(angle == 180)
				bmp = wxBitmap(m_bmp.ConvertToImage().Rotate180());
		}
		else
		{
			bmp.Create(size);
			memDC.SelectObject(bmp);
			memDC.Blit(0,0,m_size.GetWidth(),m_size.GetHeight(),&m_dc,0,0,wxCOPY,false);
			memDC.SelectObject(wxNullBitmap);
		}
	}
	else
	{
		m_dc.SelectObject(wxNullBitmap);
		bmp = m_bmp;
		m_dc.SelectObject(m_bmp);
	}
	return bmp;
}


void AnalogChart::OnPaint(wxPaintEvent& event)
{
    if(!m_isInit)
        return;
    wxPaintDC dc(m_panel);
    dc.Blit(0,0,m_bmp.GetWidth(),m_bmp.GetHeight(),&m_dc,0,0,wxCOPY,FALSE);
}


///传感器间隔：50mm
#define SENSOR_INTERVAL 50

void AnalogChart::DrawChart(float spos, float epos)
{
	if(!m_isInit)
        return;
	if(spos < 0)
		spos = 0;
	if(epos > m_jts.back().epos)
		epos = m_jts.back().epos;
	m_spos = spos;
	m_epos = epos;
	int charW = m_dc.GetCharWidth();
    int charH = m_dc.GetCharHeight();
	int eleW = m_eleBmp.GetWidth();
	int eleH = m_eleBmp.GetHeight();
	float ratio, _eleH_ = (m_size.GetHeight() - 2*charH) / (m_ropeCnt * 2.0);
	int gap = 0;
	wxBitmap bmp[6];
	if(eleH > _eleH_)
	{
		ratio = _eleH_ / eleH;
		wxImage image = m_eleBmp.ConvertToImage();
        bmp[0] = wxBitmap(image.Scale(eleW, eleH*ratio));
        image = m_brkBmp.ConvertToImage();
        bmp[1] = wxBitmap(image.Scale(m_brkBmp.GetWidth(), m_brkBmp.GetHeight()*ratio));
        for(int i=0; i<4; i++)
		{
			image = m_jtBmp[i].ConvertToImage();
			bmp[2+i] = wxBitmap(image.Scale(m_jtBmp[i].GetWidth(), m_jtBmp[i].GetHeight()*ratio));
		}
		eleH = bmp[0].GetHeight();
	}
	else
	{
		gap = (m_size.GetHeight() - 2*charH - m_ropeCnt*2*eleH) / 2;
		_eleH_ = eleH;
		bmp[0] = m_eleBmp;
		bmp[1] = m_brkBmp;
		for(int i=0; i<4; i++)
			bmp[2+i] = m_jtBmp[i];
	}
	wxMemoryDC mdc;
	wxBitmap bmp0(m_size.GetWidth(), eleH);
	mdc.SelectObject(bmp0);
	int x = 0, y, w = m_size.GetWidth(), h;
	while(x < w)
	{
		mdc.DrawBitmap(bmp[0], x, 0);
		x += eleW;
	}
    for(int i=0; i<m_ropeCnt; i++)
	{
		y = charH + gap + _eleH_/2 + i*2*_eleH_;
		m_dc.Blit(0, y, w, eleH, &mdc, 0, 0, wxCOPY, false);
	}
	mdc.SelectObject(wxNullBitmap);

	///接头不管实际长度多少，都按照不同阶数图片对应的像素数占用
	///非接头的部分，计算实际长度与像素数的比例
	float lenPs = epos - spos;
	int lv, lenPx = m_size.GetWidth();
	for(int i=0; i<m_jts.size(); i++)
	{
		if(m_jts[i].spos < epos && m_jts[i].epos > spos)
		{
			if(m_jts[i].lv < 1)
				lv = 0;
			else if(m_jts[i].lv > 4)
				lv = 3;
			else
				lv = m_jts[i].lv - 1;
			if(m_jts[i].spos < spos)
			{
				lenPs -= m_jts[i].epos - spos;
				lenPx -= bmp[2+lv].GetWidth() * ((m_jts[i].epos - spos) / (m_jts[i].epos - m_jts[i].spos));
			}
			else if(m_jts[i].epos > epos)
			{
				lenPs -= epos - m_jts[i].spos;
				lenPx -= bmp[2+lv].GetWidth() * ((epos - m_jts[i].spos) / (m_jts[i].epos - m_jts[i].spos));
			}
			else
			{
				lenPs -= m_jts[i].epos - m_jts[i].spos;
				lenPx -= bmp[2+lv].GetWidth();
			}
		}
	}
	ratio = lenPx / lenPs;

	typedef struct
	{
		float spos;
		float epos;
		int spx;
		int epx;
	} MyJtData;
	std::vector<MyJtData> mjData;
	MyJtData mjd;

	wxString str;
	m_dc.SetTextForeground(m_jtColor);

	///计算接头分割段，并画接头
	lenPs = 0;
	lenPx = 0;
	for(int i=0; i<m_jts.size(); i++)
	{
		if(m_jts[i].spos < epos && m_jts[i].epos > spos)
		{
			if(m_jts[i].lv < 1)
				lv = 0;
			else if(m_jts[i].lv > 4)
				lv = 3;
			else
				lv = m_jts[i].lv - 1;
			mjd.spos = m_jts[i].spos;
			mjd.epos = m_jts[i].epos;
			if(m_jts[i].spos < spos)
			{
				w = bmp[2+lv].GetWidth() * ((m_jts[i].epos - spos) / (m_jts[i].epos - m_jts[i].spos));
				lenPs += m_jts[i].epos - spos;
				lenPx += w;
				mjd.spx = 0;
				mjd.epx = w;
			}
			else if(m_jts[i].epos > epos)
			{
				w = bmp[2+lv].GetWidth() * ((epos - m_jts[i].spos) / (m_jts[i].epos - m_jts[i].spos));
				lenPs += epos - m_jts[i].spos;
				lenPx += w;
				mjd.spx = m_size.GetWidth() - w;
				mjd.epx = m_size.GetWidth();
			}
			else
			{
				w = bmp[2+lv].GetWidth();
				mjd.spx = lenPx + (m_jts[i].spos - spos - lenPs) * ratio;
				mjd.epx = mjd.spx + w;
				lenPs += m_jts[i].epos - m_jts[i].spos;
				lenPx += w;
			}
			mjData.push_back(mjd);
			str = _("JT-") + m_jts[i].num;
			w = str.Len() * charW;
			h = charH;
			x = (mjd.spx + mjd.epx)/2 - w/2;
			y = gap;
			m_dc.DrawLabel(str, wxRect(x,y,w,h));
			x = mjd.spx;
			y = charH + gap;
			w = bmp[2+lv].GetWidth();
			h = bmp[2+lv].GetHeight();
			wxBitmap bmpT(w, m_size.GetHeight()-2*charH-2*gap);
			mdc.SelectObject(bmpT);
			if(m_bgBmp.IsOk())
			{
				mdc.DrawBitmap(m_bgBmp,x,y);
			}
			else
			{
				mdc.SetBackground(m_bgColor);
				mdc.Clear();
			}
			while(y <= m_size.GetHeight() - charH - gap - h)
			{
				//m_dc.DrawBitmap(bmp[2+lv], x, y, true);
				mdc.DrawBitmap(bmp[2+lv], 0, y-charH-gap, true);
				y += h;
			}
			if(y < m_size.GetHeight() - charH - gap)
			{
				h = m_size.GetHeight() - charH - gap - y;
				//m_dc.DrawBitmap(bmp[2+lv].GetSubBitmap(wxRect(0, 0, w, h)), x, y, true);
				mdc.DrawBitmap(bmp[2+lv].GetSubBitmap(wxRect(0, 0, w, h)), 0, y-charH-gap, true);
			}
			m_dc.Blit(x, charH+gap, bmpT.GetWidth(), bmpT.GetHeight(), &mdc, 0, 0, wxCOPY, FALSE);
			mdc.SelectObject(wxNullBitmap);
		}
	}

	///按接头分割段，画损伤
	w = bmp[1].GetWidth();
	h = bmp[1].GetHeight();
	wxBitmap bmpB(w, h);
	mdc.SelectObject(bmpB);
	if(m_bgBmp.IsOk())
		mdc.SetBackground(*wxWHITE);
	else
		mdc.SetBackground(m_bgColor);
	mdc.Clear();
	mdc.DrawBitmap(bmp[1], 0, 0, true);
	int rope, v, k;
	if(mjData.empty())
	{
		for(int i=0; i<m_flaws.size(); i++)
		{
			if(m_flaws[i].pos > epos)
				break;
			if(m_flaws[i].pos < spos)
				continue;
			x = (m_flaws[i].pos - spos) * ratio - w/2;
			rope = (m_flaws[i].ss + m_flaws[i].ee)*0.5/SENSOR_INTERVAL * (m_ropeCnt * 1.0 / m_sensorCnt);
			y = charH + gap + _eleH_/2 + rope * 2 * _eleH_;
			//m_dc.DrawBitmap(bmp[1], x, y, true);
			m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
			v = m_flaws[i].value + 0.5;
			k = 1;
			while(v > 1)
			{
				y = charH + gap + _eleH_/2 + (rope + k) * 2 * _eleH_;
				//m_dc.DrawBitmap(bmp[1], x, y, true);
				m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
				v--;
				if(v > 1)
				{
					y = charH + gap + _eleH_/2 + (rope - k) * 2 * _eleH_;
					//m_dc.DrawBitmap(bmp[1], x, y, true);
					m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
					v--;
				}
				k++;
			}
		}
	}
	else
	{
		for(int i=0; i<m_flaws.size(); i++)
		{
			if(m_flaws[i].pos > epos)
				break;
			if(m_flaws[i].pos < spos)
				continue;
			if(m_flaws[i].pos > mjData.back().epos)
			{
				x = mjData.back().epx + (m_flaws[i].pos - mjData.back().epos) * ratio - w/2;
				rope = (m_flaws[i].ss + m_flaws[i].ee)*0.5/SENSOR_INTERVAL * (m_ropeCnt * 1.0 / m_sensorCnt);
				y = charH + gap + _eleH_/2 + rope * 2 * _eleH_;
				//m_dc.DrawBitmap(bmp[1], x, y, true);
				m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
				v = m_flaws[i].value + 0.5;
				k = 1;
				while(v > 1)
				{
					y = charH + gap + _eleH_/2 + (rope + k) * 2 * _eleH_;
					//m_dc.DrawBitmap(bmp[1], x, y, true);
					m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
					v--;
					if(v > 1)
					{
						y = charH + gap + _eleH_/2 + (rope - k) * 2 * _eleH_;
						//m_dc.DrawBitmap(bmp[1], x, y, true);
						m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
					}
					k++;
				}
			}
			else
			{
				for(int j=0; j<mjData.size(); j++)
				{
					if(m_flaws[i].pos < mjData[j].spos)
					{
						x = mjData[j].spx - (mjData[j].spos - m_flaws[i].pos) * ratio - w/2;
						rope = (m_flaws[i].ss + m_flaws[i].ee)*0.5/SENSOR_INTERVAL * (m_ropeCnt * 1.0 / m_sensorCnt);
						y = charH + gap + _eleH_/2 + rope * 2 * _eleH_;
						//m_dc.DrawBitmap(bmp[1], x, y, true);
						m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
						v = m_flaws[i].value + 0.5;
						k = 1;
						while(v > 1)
						{
							y = charH + gap + _eleH_/2 + (rope + k) * 2 * _eleH_;
							//m_dc.DrawBitmap(bmp[1], x, y, true);
							m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
							v--;
							if(v > 1)
							{
								y = charH + gap + _eleH_/2 + (rope - k) * 2 * _eleH_;
								//m_dc.DrawBitmap(bmp[1], x, y, true);
								m_dc.Blit(x, y, w, h, &mdc, 0, 0, wxCOPY, FALSE);
							}
							k++;
						}
						break;
					}
				}
			}
		}
	}

	///画下标，接头都标，非接头区按10个charW间隔
	m_dc.SetTextForeground(m_coColor);
	v = m_size.GetWidth() / (10 * charW);
	float xitv = m_size.GetWidth() * 1.0 / v, xitv2;
	h = charH;
	y = m_size.GetHeight() - charH - gap;
	if(mjData.empty())
	{
		for(int i=0; i<=v; i++)
		{
			str.Printf(_("%.0f"), spos + i*(epos-spos)/v);
			w = str.Len() * charW;
			x = i * xitv - w/2;
			m_dc.DrawLabel(str, wxRect(x,y,w,h));
		}
	}
	else
	{
		for(int i=0; i<mjData.size(); i++)
		{
			if(i == 0 && mjData[i].spx > xitv)
			{
				v = mjData[i].spx / xitv;
				xitv2 = mjData[i].spx * 1.0 / v;
				for(int j=0; j<v; j++)
				{
					str.Printf(_("%.0f"), spos + j*(mjData[i].spos-spos)/v);
					w = str.Len() * charW;
					if(j == 0)
						x = 0;
					else
						x = j * xitv2;
					m_dc.DrawLabel(str, wxRect(x,y,w,h));
				}
			}
			if(i > 0 && (mjData[i].spx - mjData[i-1].epx) > 2*xitv)
			{
				v = (mjData[i].spx - mjData[i-1].epx) / xitv;
				xitv2 = (mjData[i].spx - mjData[i-1].epx) * 1.0 / v;
				for(int j=1; j<v; j++)
				{
					str.Printf(_("%.0f"), mjData[i-1].epos + j*(mjData[i].spos-mjData[i-1].epos)/v);
					w = str.Len() * charW;
					x = mjData[i-1].epx + j * xitv2 - w/2;
					m_dc.DrawLabel(str, wxRect(x,y,w,h));
				}
			}
			str.Printf(_("%.0f"), (mjData[i].spos + mjData[i].epos)/2);
			w = str.Len() * charW;
			x = (mjData[i].spx + mjData[i].epx)/2 - w/2;
			m_dc.DrawLabel(str, wxRect(x,y,w,h));
		}
		if(m_size.GetWidth() - mjData.back().epx > xitv)
		{
			v = (m_size.GetWidth() - mjData.back().epx) / xitv;
			xitv2 = (m_size.GetWidth() - mjData.back().epx) * 1.0 / v;
			for(int j=1; j<=v; j++)
			{
				str.Printf(_("%.0f"), mjData.back().epos + j*(epos-mjData.back().epos)/v);
				w = str.Len() * charW;
				x = mjData.back().epx + j * xitv2 - w/2;
				if(x > m_size.GetWidth() - w)
					x = m_size.GetWidth() - w;
				m_dc.DrawLabel(str, wxRect(x,y,w,h));
			}
		}
	}

	if(m_parent)
	{
		///刷新
		m_panel->Refresh(false);
		m_panel->Update();
	}
}


///鼠标移动事件
void AnalogChart::OnMouseMove(wxMouseEvent& event)
{
	if(!m_isInit || m_epos < 0.001 || m_epos < m_spos)
        return;
	if(m_leftOrRight == 0)
		return;
	wxClientDC dc(m_panel);
	wxPoint tmpPoint = event.GetLogicalPosition(dc);
	int move = tmpPoint.x - m_xLogicalPos.x;
	m_xLogicalPos = tmpPoint;
	if(move < 0) //右移
	{
		if(fabs(m_epos - m_jts.back().epos) < 0.001)
			return;
		float v = (m_epos - m_spos) * (-move * 1.0 / m_size.GetWidth());
		float epos = m_epos;
		m_epos += v;
		if(m_epos > m_jts.back().epos)
		{
			m_epos = m_jts.back().epos;
			v = m_epos - epos;
		}
		m_spos += v;
	}
	else if(move > 0) //左移
	{
		if(m_spos < 0.001)
			return;
		float v = (m_epos - m_spos) * (move * 1.0 / m_size.GetWidth());
		float spos = m_spos;
		m_spos -= v;
		if(m_spos < 0)
		{
			m_spos = 0;
			v = spos;
		}
		m_epos -= v;
	}
	Initial();
	DrawChart(m_spos, m_epos);
}

///鼠标左键按下事件
void AnalogChart::OnLeftDown(wxMouseEvent& event)
{
	if(!m_isInit || m_epos < 0.001 || m_epos < m_spos)
		return;
	m_leftOrRight = 1;
	if(!m_panel->HasFocus())
        m_panel->SetFocus();
	//记录左键按下坐标
	wxClientDC dc(m_panel);
    m_xLogicalPos = m_yLogicalPos = event.GetLogicalPosition(dc);
}
///鼠标左键弹起事件
void AnalogChart::OnLeftUp(wxMouseEvent& event)
{
	if(!m_isInit || m_epos < 0.001 || m_epos < m_spos)
		return;
	m_leftOrRight = 0;
}

//数标滚动
void AnalogChart::OnMouseWheel(wxMouseEvent& event)
{
    if(!m_isInit || m_epos < 0.001 || m_epos < m_spos)
        return;
    wxClientDC dc(m_panel);
    //判断鼠标滚轮上滚还是下滚
    if(event.GetWheelRotation() > 0)
	{
		float v = (m_epos - m_spos) * 0.1;
		if(v < 1.0) //最大放大到屏显10米长
			return;
		m_spos += v;
		m_epos -= v;
    }
	else
	{
		if(m_spos < 0.001 && fabs(m_epos - m_jts.back().epos) < 0.001)
			return;
		float v = (m_epos - m_spos) * 0.1;
		m_spos -= v;
		if(m_spos < 0)
			m_spos = 0;
		m_epos += v;
		if(m_epos > m_jts.back().epos)
			m_epos = m_jts.back().epos;
	}
	Initial();
	DrawChart(m_spos, m_epos);
}


///外部调用--曲线放大、缩小、左翻、右翻
// move: 0=不移动，-1=左翻一屏，1=右翻一屏
// zoom: >0=X轴缩放，1.0=X轴不缩放，>1.0=放大，<1.0=缩小
void AnalogChart::MoveAndZoom(int move, float zoom)
{
	if(!m_isInit || m_epos < 0.001 || m_epos < m_spos)
        return;
	bool isOK = false;
	if(move < 0 && m_spos > 0.001)
	{
		float v = m_epos - m_spos;
		float spos = m_spos;
		m_spos -= v;
		if(m_spos < 0)
		{
			m_spos = 0;
			v = spos;
		}
		m_epos -= v;
		isOK = true;
	}
	else if(move > 0 && m_epos < m_jts.back().epos)
	{
		float v = m_epos - m_spos;
		float epos = m_epos;
		m_epos += v;
		if(m_epos > m_jts.back().epos)
		{
			m_epos = m_jts.back().epos;
			v = m_epos - epos;
		}
		m_spos += v;
		isOK = true;
	}
	if(zoom > 1.000 && zoom <= 100)
	{
		float v = (m_epos - m_spos) * (zoom - 1) / zoom;
		if(m_epos - m_spos - v >= 10.0) //最大放大到屏显10米长
		{
			m_spos += v/2;
			m_epos -= v/2;
			isOK = true;
		}
	}
	else if(zoom >= 0.01 && zoom < 1.000)
	{
		if(m_spos > 0.001 || m_epos < m_jts.back().epos)
		{
			float v = (m_epos - m_spos) * (1 - zoom) / zoom;
			m_spos -= v/2;
			if(m_spos < 0)
				m_spos = 0;
			m_epos += v/2;
			if(m_epos > m_jts.back().epos)
				m_epos = m_jts.back().epos;
			isOK = true;
		}
	}
	if(isOK)
	{
		Initial();
		DrawChart(m_spos, m_epos);
	}
}

