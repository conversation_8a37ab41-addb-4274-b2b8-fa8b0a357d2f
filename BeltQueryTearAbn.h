#ifndef BELTQUERYTEARABN_H
#define BELTQUERYTEARABN_H

//(*Headers(BeltQueryTearAbn)
#include <wx/dialog.h>
#include <wx/grid.h>
#include <wx/stattext.h>
//*)
#include "yblib.h"

class BeltQueryTearAbn: public wxDialog
{
	public:

		BeltQueryTearAbn(wxWindow* parent,wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltQueryTearAbn();

		void SetData(Redis& jdata);

		//(*Declarations(BeltQueryTearAbn)
		wxGrid* Grid1;
		wxGrid* Grid2;
		wxStaticText* StaticText1;
		wxStaticText* StaticText2;
		//*)

	protected:

		//(*Identifiers(BeltQueryTearAbn)
		static const long ID_GRID1;
		static const long ID_GRID2;
		static const long ID_STATICTEXT1;
		static const long ID_STATICTEXT2;
		//*)

	private:

		//(*Handlers(BeltQueryTearAbn)
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
