#ifndef DEBUGWIN_H
#define DEBUGWIN_H

//(*Headers(DebugWin)
#include <wx/dialog.h>
#include <wx/textctrl.h>
//*)
#include "yblib.h"

extern std::ostream *g_debug_stream;
#define DBGS g_debug_stream

class DebugWin: public wxDialog
{
	public:

		DebugWin(wxWindow* parent,wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~DebugWin();

		//(*Declarations(DebugWin)
		wxTextCtrl* TextCtrl1;
		//*)

	protected:

		//(*Identifiers(DebugWin)
		static const long ID_TEXTCTRL1;
		//*)

	private:

		//(*Handlers(DebugWin)
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
