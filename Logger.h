#ifndef LOGGER_H
#define LOGGER_H

#include <fstream>
#include <mutex>
#include <string>

/**
 * 简单的日志管理器类
 * 支持文件和控制台双重输出，线程安全
 */
class SimpleLogger {
private:
    std::ofstream logFile;
    std::mutex logMutex;
    bool fileLoggingEnabled = true;
    bool consoleLoggingEnabled = true;

    /**
     * 获取当前时间字符串
     * @return 格式化的时间字符串
     */
    std::string GetCurrentTime();

public:
    /**
     * 初始化日志系统
     * @param logFilePath 日志文件路径
     * @return 成功返回true，失败返回false
     */
    bool Initialize(const std::string& logFilePath);

    /**
     * 记录日志
     * @param level 日志级别
     * @param msg 日志消息
     */
    void Log(const std::string& level, const std::string& msg);

    /**
     * 记录信息日志
     * @param msg 日志消息
     */
    void LogInfo(const std::string& msg);

    /**
     * 记录错误日志
     * @param msg 日志消息
     */
    void LogError(const std::string& msg);

    /**
     * 记录警告日志
     * @param msg 日志消息
     */
    void LogWarning(const std::string& msg);

    /**
     * 记录调试日志
     * @param msg 日志消息
     */
    void LogDebug(const std::string& msg);

    /**
     * 设置控制台日志输出
     * @param enabled 是否启用
     */
    void SetConsoleLogging(bool enabled);

    /**
     * 设置文件日志输出
     * @param enabled 是否启用
     */
    void SetFileLogging(bool enabled);

    /**
     * 析构函数，自动关闭日志文件
     */
    ~SimpleLogger();
};

// 全局日志实例声明
extern SimpleLogger g_logger;

// 简单的日志函数
void LogMessage(const std::string& msg);
void LogError(const std::string& msg);
void LogWarning(const std::string& msg);

#endif // LOGGER_H
