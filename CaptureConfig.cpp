#include "CaptureConfig.h"
#include <wx/filename.h>
#include "Logger.h"

CaptureConfig::CaptureConfig()
    : m_config(nullptr)
{
    SetDefaults();
}

CaptureConfig::~CaptureConfig()
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }
}

bool CaptureConfig::LoadConfig(const wxString& configFile)
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }

    if (!wxFileName::FileExists(configFile))
    {
        // 配置文件不存在，使用默认值并创建配置文件
        LogMessage("配置文件不存在，使用默认配置: " + configFile.ToStdString());
        return SaveConfig(configFile);
    }

    m_config = new wxFileConfig(wxEmptyString, wxEmptyString, configFile);
    if (!m_config)
    {
        LogError("无法打开配置文件: " + configFile.ToStdString());
        return false;
    }

    // 读取摄像头配置
    m_cameraIP = m_config->Read("/Camera/IP", m_cameraIP);
    m_cameraPort = m_config->ReadLong("/Camera/Port", m_cameraPort);
    m_cameraUsername = m_config->Read("/Camera/Username", m_cameraUsername);
    m_cameraPassword = m_config->Read("/Camera/Password", m_cameraPassword);
    m_cameraResolution = m_config->Read("/Camera/Resolution", m_cameraResolution);
    m_cameraFrameRate = m_config->ReadLong("/Camera/FrameRate", m_cameraFrameRate);

    // 读取拍照配置
    m_cameraDistance = m_config->ReadDouble("/Capture/CameraDistance", m_cameraDistance);
    m_safetyBuffer = m_config->ReadDouble("/Capture/SafetyBuffer", m_safetyBuffer);
    m_photoQuality = m_config->ReadLong("/Capture/PhotoQuality", m_photoQuality);
    m_autoCaptureEnabled = m_config->ReadBool("/Capture/AutoCaptureEnabled", m_autoCaptureEnabled);
    m_photoRootPath = m_config->Read("/Capture/PhotoRootPath", m_photoRootPath);

    LogMessage("配置文件加载成功: " + configFile.ToStdString());
    // 打印configFile绝对路径
    wxFileName fileName(configFile);
    fileName.MakeAbsolute();
    LogMessage("配置文件绝对路径: " + fileName.GetFullPath().ToStdString());
    
    return true;
}

bool CaptureConfig::SaveConfig(const wxString& configFile)
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }

    m_config = new wxFileConfig(wxEmptyString, wxEmptyString, configFile);
    if (!m_config)
    {
        LogError("无法创建配置文件: " + configFile.ToStdString());
        return false;
    }

    // 保存摄像头配置
    m_config->Write("/Camera/IP", m_cameraIP);
    m_config->Write("/Camera/Port", (long)m_cameraPort);
    m_config->Write("/Camera/Username", m_cameraUsername);
    m_config->Write("/Camera/Password", m_cameraPassword);
    m_config->Write("/Camera/Resolution", m_cameraResolution);
    m_config->Write("/Camera/FrameRate", (long)m_cameraFrameRate);

    // 保存拍照配置
    m_config->Write("/Capture/CameraDistance", m_cameraDistance);
    m_config->Write("/Capture/SafetyBuffer", m_safetyBuffer);
    m_config->Write("/Capture/PhotoQuality", (long)m_photoQuality);
    m_config->Write("/Capture/AutoCaptureEnabled", m_autoCaptureEnabled);
    m_config->Write("/Capture/PhotoRootPath", m_photoRootPath);

    // 强制写入文件
    m_config->Flush();

    LogMessage("配置文件保存成功: " + configFile.ToStdString());
    return true;
}

CaptureConfig CaptureConfig::GetDefaultConfig()
{
    CaptureConfig config;
    config.SetDefaults();
    return config;
}

void CaptureConfig::SetDefaults()
{
    // 摄像头默认配置
    m_cameraIP = "************";
    m_cameraPort = 8000;
    m_cameraUsername = "admin";
    m_cameraPassword = "Tck123456";
    m_cameraResolution = "2560x1440";
    m_cameraFrameRate = 30;

    // 拍照默认配置
    m_cameraDistance = 2.0f;
    m_safetyBuffer = 0.5f;
    m_photoQuality = 95;
    m_autoCaptureEnabled = true;
    m_photoRootPath = "photos";
}
