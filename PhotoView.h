#ifndef PHOTOVIEW_H
#define PHOTOVIEW_H

//(*Headers(PhotoView)
#include <wx/dialog.h>
//*)
#include <wx/bitmap.h>

class PhotoView: public wxDialog
{
	public:

		PhotoView(wxWindow* parent, const wxBitmap& bmp, const wxSize& size=wxDefaultSize);
		virtual ~PhotoView();

		//(*Declarations(PhotoView)
		//*)

	protected:

		//(*Identifiers(PhotoView)
		//*)

	private:

		wxBitmap m_bitmap_src;
		wxBitmap m_bitmap;
		int m_win_w;
		int m_win_h;
		int m_draw_x;
		int m_draw_y;
		wxPoint m_logicalPos;
        bool m_isLeftDown;

		//(*Handlers(PhotoView)
		void OnPaint(wxPaintEvent& event);
		void OnLeftDown(wxMouseEvent& event);
		void OnLeftUp(wxMouseEvent& event);
		void OnMouseMove(wxMouseEvent& event);
		void OnMouseWheel(wxMouseEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
