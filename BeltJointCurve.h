#ifndef BELTJOINTCURVE_H
#define BELTJOINTCURVE_H

//(*Headers(BeltJointCurve)
#include <wx/dialog.h>
#include <wx/statbox.h>
//*)
#include "wxCurve.h"

class BeltJointCurve: public wxDialog
{
	public:

		BeltJointCurve(wxWindow* parent, wxString& ybfile, wxString& jtfile, float startPos, float endPos, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltJointCurve();

		//(*Declarations(BeltJointCurve)
		wxStaticBox* StaticBox1;
		wxStaticBox* StaticBox2;
		//*)

	protected:

		//(*Identifiers(BeltJointCurve)
		static const long ID_STATICBOX1;
		static const long ID_STATICBOX2;
		//*)

	private:

		wxCurve *m_curve_yb;
		wxCurve *m_curve_jt;

		//(*Handlers(BeltJointCurve)
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
