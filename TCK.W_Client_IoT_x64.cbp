<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="TCK.W_Client_IoT_x64" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/TCKW_Client_IoT_x64" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Option projectLinkerOptionsRelation="2" />
				<Compiler>
					<Add option="-g" />
					<Add option="-D__WXDEBUG__" />
					<Add option="-DDEBUG_OUTPUT" />
					<Add directory="$(#wx.lib)/mswu" />
					<Add directory="include" />
				</Compiler>
				<ResourceCompiler>
					<Add directory="$(#wx.lib)/mswu" />
				</ResourceCompiler>
				<Linker>
					<Add option="-static-libstdc++" />
					<Add option="-static-libgcc" />
					<Add option="-static" />
					<Add library="libwxmsw30u_xrc.a" />
					<Add library="libwxmsw30u_aui.a" />
					<Add library="libwxmsw30u_media.a" />
					<Add library="libwxbase30u_net.a" />
					<Add library="libwxbase30u_xml.a" />
					<Add library="libwxmsw30u_adv.a" />
					<Add library="libwxmsw30u_html.a" />
					<Add library="libwxmsw30u_core.a" />
					<Add library="libwxbase30u.a" />
					<Add library="libwxpng.a" />
					<Add library="libwxjpeg.a" />
					<Add library="libwxtiff.a" />
					<Add library="libwxzlib.a" />
					<Add library="libwxexpat.a" />
					<Add library="dllyb_win_x64" />
					<Add directory="$(#wx.lib)" />
					<Add directory="lib" />
				</Linker>
			</Target>
			<Target title="Release">
				<Option output="bin/Release/TCKW_Client_IoT_x64" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Release/" />
				<Option type="0" />
				<Option compiler="gnu_gcc_compiler_x64" />
				<Option projectLinkerOptionsRelation="2" />
				<Compiler>
					<Add option="-O2" />
					<Add directory="$(#wx.lib)/mswu" />
					<Add directory="include" />
					<Add directory="D:/CodeBlocks-17.12/wxWidgets-3.0.2/include/msvc/wx" />
				</Compiler>
				<ResourceCompiler>
					<Add directory="$(#wx.lib)/mswu" />
				</ResourceCompiler>
				<Linker>
					<Add option="-s" />
					<Add option="-static-libstdc++" />
					<Add option="-static-libgcc" />
					<Add option="-static" />
					<Add library="Curve_simple_x64" />
					<Add library="libwxmsw30u_xrc.a" />
					<Add library="libwxmsw30u_aui.a" />
					<Add library="libwxmsw30u_media.a" />
					<Add library="libwxbase30u_net.a" />
					<Add library="libwxbase30u_xml.a" />
					<Add library="libwxmsw30u_adv.a" />
					<Add library="libwxmsw30u_html.a" />
					<Add library="libwxmsw30u_core.a" />
					<Add library="libwxbase30u.a" />
					<Add library="libwxpng.a" />
					<Add library="libwxjpeg.a" />
					<Add library="libwxtiff.a" />
					<Add library="libwxzlib.a" />
					<Add library="libwxexpat.a" />
					<Add library="dllyb_win_x64" />
					<Add library="pthread" />
					<Add library="iphlpapi" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/GdiPlus.lib" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/HCCore.lib" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/HCNetSDK.lib" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/PlayCtrl.lib" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/HCNetSDKCom/HCAlarm.lib" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/HCNetSDKCom/HCGeneralCfgMgr.lib" />
					<Add library="D:/code/client_branches/add_joint_capture/lib/HCNetSDKCom/HCPreview.lib" />
					<Add directory="$(#wx.lib)" />
					<Add directory="lib" />
					<Add directory="lib/HCNetSDKCom" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-pipe" />
			<Add option="-mthreads" />
			<Add option="-D__GNUWIN32__" />
			<Add option="-D__WXMSW__" />
			<Add option="-DwxUSE_UNICODE" />
			<Add option='[[if (PLATFORM == PLATFORM_MSW &amp;&amp; (GetCompilerFactory().GetCompilerVersionString(_T(&quot;gcc&quot;)) &gt;= _T(&quot;4.0.0&quot;))) print(_T(&quot;-Wno-attributes&quot;));]]' />
			<Add option="-Wall" />
			<Add directory="$(#wx)/include" />
			<Add directory="$(#wx)/contrib/include" />
		</Compiler>
		<ResourceCompiler>
			<Add directory="$(#wx)/include" />
		</ResourceCompiler>
		<Linker>
			<Add option="-mthreads" />
			<Add library="libkernel32.a" />
			<Add library="libuser32.a" />
			<Add library="libgdi32.a" />
			<Add library="libwinspool.a" />
			<Add library="libcomdlg32.a" />
			<Add library="libadvapi32.a" />
			<Add library="libshell32.a" />
			<Add library="libole32.a" />
			<Add library="liboleaut32.a" />
			<Add library="libuuid.a" />
			<Add library="libcomctl32.a" />
			<Add library="libwsock32.a" />
			<Add library="libodbc32.a" />
		</Linker>
		<Unit filename="AccountDialog.cpp" />
		<Unit filename="AccountDialog.h" />
		<Unit filename="BeltChart.cpp" />
		<Unit filename="BeltChart.h" />
		<Unit filename="BeltCurve.cpp" />
		<Unit filename="BeltCurve.h" />
		<Unit filename="BeltFrame.cpp" />
		<Unit filename="BeltFrame.h" />
		<Unit filename="BeltJointCurve.cpp" />
		<Unit filename="BeltJointCurve.h" />
		<Unit filename="BeltQueryAlarm.cpp" />
		<Unit filename="BeltQueryAlarm.h" />
		<Unit filename="BeltQueryFlaw.cpp" />
		<Unit filename="BeltQueryFlaw.h" />
		<Unit filename="BeltQueryJoint.cpp" />
		<Unit filename="BeltQueryJoint.h" />
		<Unit filename="BeltQueryTear.cpp" />
		<Unit filename="BeltQueryTear.h" />
		<Unit filename="BeltQueryTearAbn.cpp" />
		<Unit filename="BeltQueryTearAbn.h" />
		<Unit filename="BeltReport.cpp" />
		<Unit filename="BeltReport.h" />
		<Unit filename="BeltTearReport.cpp" />
		<Unit filename="BeltTearReport.h" />
		<Unit filename="BeltYBRedoSelDate.cpp" />
		<Unit filename="BeltYBRedoSelDate.h" />
		<Unit filename="CameraManager.cpp" />
		<Unit filename="CameraManager.h" />
		<Unit filename="CaptureConfig.cpp" />
		<Unit filename="CaptureConfig.h" />
		<Unit filename="CircleManager.cpp" />
		<Unit filename="CircleManager.h" />
		<Unit filename="Control/BitmapButton.cpp" />
		<Unit filename="Control/BitmapButton.h" />
		<Unit filename="Control/MediaAudio.cpp" />
		<Unit filename="Control/MediaAudio.h" />
		<Unit filename="Control/TransStaticBmp.cpp" />
		<Unit filename="Control/TransStaticBmp.h" />
		<Unit filename="Control/TransStaticText.cpp" />
		<Unit filename="Control/TransStaticText.h" />
		<Unit filename="Control/TransWin.cpp" />
		<Unit filename="Control/TransWin.h" />
		<Unit filename="DebugWin.cpp" />
		<Unit filename="DebugWin.h" />
		<Unit filename="JointCaptureController.cpp" />
		<Unit filename="JointCaptureController.h" />
		<Unit filename="Logger.cpp" />
		<Unit filename="Logger.h" />
		<Unit filename="MainFrame.cpp" />
		<Unit filename="MainFrame.h" />
		<Unit filename="NoteWin.cpp" />
		<Unit filename="NoteWin.h" />
		<Unit filename="PhotoStorageManager.cpp" />
		<Unit filename="PhotoStorageManager.h" />
		<Unit filename="PhotoView.cpp" />
		<Unit filename="PhotoView.h" />
		<Unit filename="PhotoViewer.cpp" />
		<Unit filename="PhotoViewer.h" />
		<Unit filename="RunningView.cpp" />
		<Unit filename="RunningView.h" />
		<Unit filename="StatChart.cpp" />
		<Unit filename="StatChart.h" />
		<Unit filename="TCK_W_Client_IoTApp.cpp" />
		<Unit filename="TCK_W_Client_IoTApp.h" />
		<Unit filename="TCK_W_Client_IoTMain.cpp" />
		<Unit filename="TCK_W_Client_IoTMain.h" />
		<Unit filename="Validity.cpp" />
		<Unit filename="Validity.h" />
		<Unit filename="ZXQueryAlarm.cpp" />
		<Unit filename="ZXQueryAlarm.h" />
		<Unit filename="ZXQueryFlaw.cpp" />
		<Unit filename="ZXQueryFlaw.h" />
		<Unit filename="ZXReport.cpp" />
		<Unit filename="ZXReport.h" />
		<Unit filename="ZXStatDialog.cpp" />
		<Unit filename="ZXStatDialog.h" />
		<Unit filename="analogChart.cpp" />
		<Unit filename="analogChart.h" />
		<Unit filename="resource.rc">
			<Option compilerVar="WINDRES" />
		</Unit>
		<Unit filename="tcp_client_srcdata.cpp" />
		<Unit filename="tcp_client_srcdata.h" />
		<Unit filename="tcp_client_view.cpp" />
		<Unit filename="tcp_client_view.h" />
		<Unit filename="tcp_client_web.cpp" />
		<Unit filename="tcp_client_web.h" />
		<Unit filename="wxsmith/TCK_W_Client_IoTdialog.wxs" />
		<Extensions>
			<code_completion />
			<envvars />
			<debugger />
			<wxsmith version="1">
				<gui name="wxWidgets" src="TCK_W_Client_IoTApp.cpp" main="TCK_W_Client_IoTDialog" init_handlers="necessary" language="CPP" />
				<resources>
					<wxDialog wxs="wxsmith/TCK_W_Client_IoTdialog.wxs" src="TCK_W_Client_IoTMain.cpp" hdr="TCK_W_Client_IoTMain.h" fwddecl="0" i18n="1" name="TCK_W_Client_IoTDialog" language="CPP" />
					<wxDialog wxs="wxsmith/DebugWin.wxs" src="DebugWin.cpp" hdr="DebugWin.h" fwddecl="0" i18n="1" name="DebugWin" language="CPP" />
					<wxFrame wxs="wxsmith/MainFrame.wxs" src="MainFrame.cpp" hdr="MainFrame.h" fwddecl="0" i18n="1" name="MainFrame" language="CPP" />
					<wxFrame wxs="wxsmith/BeltFrame.wxs" src="BeltFrame.cpp" hdr="BeltFrame.h" fwddecl="0" i18n="1" name="BeltFrame" language="CPP" />
					<wxDialog wxs="wxsmith/AccountDialog.wxs" src="AccountDialog.cpp" hdr="AccountDialog.h" fwddecl="0" i18n="1" name="AccountDialog" language="CPP" />
					<wxDialog wxs="wxsmith/BeltQueryFlaw.wxs" src="BeltQueryFlaw.cpp" hdr="BeltQueryFlaw.h" fwddecl="0" i18n="1" name="BeltQueryFlaw" language="CPP" />
					<wxDialog wxs="wxsmith/BeltQueryJoint.wxs" src="BeltQueryJoint.cpp" hdr="BeltQueryJoint.h" fwddecl="0" i18n="1" name="BeltQueryJoint" language="CPP" />
					<wxDialog wxs="wxsmith/BeltJointCurve.wxs" src="BeltJointCurve.cpp" hdr="BeltJointCurve.h" fwddecl="0" i18n="1" name="BeltJointCurve" language="CPP" />
					<wxDialog wxs="wxsmith/BeltQueryAlarm.wxs" src="BeltQueryAlarm.cpp" hdr="BeltQueryAlarm.h" fwddecl="0" i18n="1" name="BeltQueryAlarm" language="CPP" />
					<wxDialog wxs="wxsmith/BeltYBRedoSelDate.wxs" src="BeltYBRedoSelDate.cpp" hdr="BeltYBRedoSelDate.h" fwddecl="0" i18n="1" name="BeltYBRedoSelDate" language="CPP" />
					<wxDialog wxs="wxsmith/BeltReport.wxs" src="BeltReport.cpp" hdr="BeltReport.h" fwddecl="0" i18n="1" name="BeltReport" language="CPP" />
					<wxDialog wxs="wxsmith/BeltTearReport.wxs" src="BeltTearReport.cpp" hdr="BeltTearReport.h" fwddecl="0" i18n="1" name="BeltTearReport" language="CPP" />
					<wxDialog wxs="wxsmith/BeltQueryTear.wxs" src="BeltQueryTear.cpp" hdr="BeltQueryTear.h" fwddecl="0" i18n="1" name="BeltQueryTear" language="CPP" />
					<wxDialog wxs="wxsmith/Validity.wxs" src="Validity.cpp" hdr="Validity.h" fwddecl="0" i18n="1" name="Validity" language="CPP" />
					<wxDialog wxs="wxsmith/BeltQueryTearAbn.wxs" src="BeltQueryTearAbn.cpp" hdr="BeltQueryTearAbn.h" fwddecl="0" i18n="1" name="BeltQueryTearAbn" language="CPP" />
					<wxDialog wxs="wxsmith/ZXQueryFlaw.wxs" src="ZXQueryFlaw.cpp" hdr="ZXQueryFlaw.h" fwddecl="0" i18n="1" name="ZXQueryFlaw" language="CPP" />
					<wxDialog wxs="wxsmith/ZXStatDialog.wxs" src="ZXStatDialog.cpp" hdr="ZXStatDialog.h" fwddecl="0" i18n="1" name="ZXStatDialog" language="CPP" />
					<wxDialog wxs="wxsmith/ZXReport.wxs" src="ZXReport.cpp" hdr="ZXReport.h" fwddecl="0" i18n="1" name="ZXReport" language="CPP" />
					<wxDialog wxs="wxsmith/ZXQueryAlarm.wxs" src="ZXQueryAlarm.cpp" hdr="ZXQueryAlarm.h" fwddecl="0" i18n="1" name="ZXQueryAlarm" language="CPP" />
				</resources>
			</wxsmith>
		</Extensions>
	</Project>
</CodeBlocks_project_file>
