/***************************************************************
 * Name:      TCK_W_Client_IoTMain.h
 * Purpose:   Defines Application Frame
 * Author:     ()
 * Created:   2018-12-05
 * Copyright:  ()
 * License:
 **************************************************************/

#ifndef TCK_W_CLIENT_IOTMAIN_H
#define TCK_W_CLIENT_IOTMAIN_H

//(*Headers(TCK_W_Client_IoTDialog)
#include <wx/button.h>
#include <wx/dialog.h>
#include <wx/stattext.h>
#include <wx/textctrl.h>
//*)

class TCK_W_Client_IoTDialog: public wxDialog
{
    public:

        TCK_W_Client_IoTDialog(wxWindow* parent,wxWindowID id = -1);
        virtual ~TCK_W_Client_IoTDialog();

    private:

        //(*Handlers(TCK_W_Client_IoTDialog)
        void OnButton1Click(wxCommandEvent& event);
        //*)
        void OnLoginReply(wxCommandEvent& event);

        //(*Identifiers(TCK_W_Client_IoTDialog)
        static const long ID_STATICTEXT1;
        static const long ID_TEXTCTRL2;
        static const long ID_STATICTEXT3;
        static const long ID_TEXTCTRL3;
        static const long ID_BUTTON1;
        //*)

        //(*Declarations(TCK_W_Client_IoTDialog)
        wxButton* Button1;
        wxStaticText* StaticText1;
        wxStaticText* StaticText3;
        wxTextCtrl* TextCtrl2;
        wxTextCtrl* TextCtrl3;
        //*)

        DECLARE_EVENT_TABLE()
};

#endif // TCK_W_CLIENT_IOTMAIN_H
