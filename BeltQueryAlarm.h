#ifndef BELTQUERYALARM_H
#define BELTQUERYALARM_H

//(*Headers(BeltQueryAlarm)
#include <wx/button.h>
#include <wx/dialog.h>
#include <wx/gauge.h>
#include <wx/grid.h>
#include <wx/stattext.h>
//*)
#include "yblib.h"
#include <string>

class BeltQueryAlarm: public wxDialog
{
	public:

		BeltQueryAlarm(wxWindow* parent, const char *sn, const char *node, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltQueryAlarm();

		void SetData(Redis& jdata);
		void ResetReply(int reply);

		//(*Declarations(BeltQueryAlarm)
		wxButton* Button1;
		wxGauge* Gauge1;
		wxGrid* Grid1;
		wxGrid* Grid2;
		wxGrid* Grid3;
		wxStaticText* StaticText1;
		wxStaticText* StaticText2;
		wxStaticText* StaticText3;
		//*)

	protected:

		//(*Identifiers(BeltQueryAlarm)
		static const long ID_GRID1;
		static const long ID_GRID2;
		static const long ID_GRID3;
		static const long ID_STATICTEXT1;
		static const long ID_STATICTEXT2;
		static const long ID_STATICTEXT3;
		static const long ID_GAUGE1;
		static const long ID_BUTTON1;
		//*)

	private:

		std::string m_sn;
		std::string m_node;
		int m_data_flag;

		//(*Handlers(BeltQueryAlarm)
		void OnButton1Click(wxCommandEvent& event);
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
