#ifndef BITMAPBUTTON_H
#define BITMAPBUTTON_H

#include <wx/button.h>

class BitmapButton : public wxButton
{
    public:
        BitmapButton(wxWindow *parent, const wxBitmap &resBitmap, const wxPoint &pos=wxDefaultPosition, const wxSize &size=wxDefaultSize, wxBitmap *pbgBmp=NULL, const wxString &text=_(""), const wxFont *txtFont=NULL, const wxColour *txtColor=NULL, const wxPoint *txtPoint=NULL);
		virtual ~BitmapButton();
        virtual bool Enable(bool enable = true);
        void ChangeText(const wxString &text=_(""), wxFont *txtFont=NULL, wxColour *txtColor=NULL, wxPoint *txtPoint=NULL);
		wxString GetText();
		void SetBitmap(const wxBitmap &bitmap);
		void SetPosition( const wxPoint &  pt );
        void SetSize( const wxSize &  size );
    private:
        void OnPaint(wxPaintEvent& event);
        void OnEnterWindow(wxMouseEvent& event);
        void OnLeaveWindow(wxMouseEvent& event);
        void OnLeftDown(wxMouseEvent& event);
        void OnLeftUp(wxMouseEvent& event);

    private:
        wxWindow *m_parent;
        wxPoint m_pos;
        wxSize m_size;
        wxBitmap m_resBitmap;
        wxBitmap *m_pbgBmp;
        wxBitmap m_bmp;
        wxBitmap m_coverBitmap;
        wxBitmap m_pressBitmap;
        wxBitmap m_forbidBitmap;
        bool m_enable;
        int m_state;
        wxString m_text;
        wxFont m_txtFont;
        wxColour m_txtColor;
        wxPoint m_txtPoint;
};

#endif // BITMAPBUTTON_H
