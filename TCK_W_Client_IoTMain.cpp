/***************************************************************
 * Name:      TCK_W_Client_IoTMain.cpp
 * Purpose:   Code for Application Frame
 * Author:     ()
 * Created:   2018-12-05
 * Copyright:  ()
 * License:
 **************************************************************/

#include "TCK_W_Client_IoTMain.h"
#include <wx/msgdlg.h>
#include <wx/xrc/xmlres.h>

//(*InternalHeaders(TCK_W_Client_IoTDialog)
#include <wx/intl.h>
#include <wx/string.h>
//*)

//helper functions
enum wxbuildinfoformat {
    short_f, long_f };

wxString wxbuildinfo(wxbuildinfoformat format)
{
    wxString wxbuild(wxVERSION_STRING);

    if (format == long_f )
    {
#if defined(__WXMSW__)
        wxbuild << _T("-Windows");
#elif defined(__UNIX__)
        wxbuild << _T("-Linux");
#endif

#if wxUSE_UNICODE
        wxbuild << _T("-Unicode build");
#else
        wxbuild << _T("-ANSI build");
#endif // wxUSE_UNICODE
    }

    return wxbuild;
}

//(*IdInit(TCK_W_Client_IoTDialog)
const long TCK_W_Client_IoTDialog::ID_STATICTEXT1 = wxNewId();
const long TCK_W_Client_IoTDialog::ID_TEXTCTRL2 = wxNewId();
const long TCK_W_Client_IoTDialog::ID_STATICTEXT3 = wxNewId();
const long TCK_W_Client_IoTDialog::ID_TEXTCTRL3 = wxNewId();
const long TCK_W_Client_IoTDialog::ID_BUTTON1 = wxNewId();
//*)

#include "tcp_client_web.h"
extern TcpClientWeb *g_tcp_web;

BEGIN_DECLARE_EVENT_TYPES()
    DECLARE_EVENT_TYPE(wxEVT_Login_Reply, TcpClientWeb::Evt_Login)
END_DECLARE_EVENT_TYPES()

DEFINE_EVENT_TYPE(wxEVT_Login_Reply)

#define EVT_LOGIN_REPLY(id, fn) \
    DECLARE_EVENT_TABLE_ENTRY( \
        wxEVT_Login_Reply, id, wxID_ANY, \
        (wxObjectEventFunction)(wxEventFunction)wxStaticCastEvent( wxCommandEventFunction, &fn ), \
        (wxObject *) NULL),

BEGIN_EVENT_TABLE(TCK_W_Client_IoTDialog,wxDialog)
    //(*EventTable(TCK_W_Client_IoTDialog)
    //*)
    EVT_LOGIN_REPLY(wxID_ANY, TCK_W_Client_IoTDialog::OnLoginReply)
END_EVENT_TABLE()

extern wxEvtHandler *g_tcp_evtHandler;

TCK_W_Client_IoTDialog::TCK_W_Client_IoTDialog(wxWindow* parent,wxWindowID id)
{
    //(*Initialize(TCK_W_Client_IoTDialog)
    Create(parent, id, _("Login"), wxDefaultPosition, wxDefaultSize, wxCAPTION|wxCLOSE_BOX, _T("id"));
    SetClientSize(wxSize(232,130));
    StaticText1 = new wxStaticText(this, ID_STATICTEXT1, _("User name :"), wxPoint(12,28), wxDefaultSize, 0, _T("ID_STATICTEXT1"));
    TextCtrl2 = new wxTextCtrl(this, ID_TEXTCTRL2, wxEmptyString, wxPoint(88,24), wxSize(132,22), 0, wxDefaultValidator, _T("ID_TEXTCTRL2"));
    StaticText3 = new wxStaticText(this, ID_STATICTEXT3, _("Pasword :"), wxPoint(24,60), wxDefaultSize, 0, _T("ID_STATICTEXT3"));
    TextCtrl3 = new wxTextCtrl(this, ID_TEXTCTRL3, wxEmptyString, wxPoint(88,56), wxSize(132,22), wxTE_PASSWORD, wxDefaultValidator, _T("ID_TEXTCTRL3"));
    Button1 = new wxButton(this, ID_BUTTON1, _("Login"), wxPoint(76,92), wxSize(75,28), 0, wxDefaultValidator, _T("ID_BUTTON1"));
    Center();

    Connect(ID_BUTTON1,wxEVT_COMMAND_BUTTON_CLICKED,(wxObjectEventFunction)&TCK_W_Client_IoTDialog::OnButton1Click);
    //*)

    wxIcon FrameIcon;
	FrameIcon.CopyFromBitmap(wxXmlResource::Get()->LoadBitmap(wxT("tck")));
	SetIcon(FrameIcon);

    g_tcp_evtHandler = this;

    if(wxFileExists("pwd.txt"))
	{
		wxFile wxf("pwd.txt");
		wxString str;
		if(wxf.ReadAll(&str))
		{
			int idx = str.Find('\n');
			if(idx > 0)
			{
				TextCtrl2->SetValue(str.Mid(0, idx).Trim(false).Trim(true));
				TextCtrl3->SetValue(str.Mid(idx+1).Trim(false).Trim(true));
				wxCommandEvent evt;
				OnButton1Click(evt);
			}
		}
		wxf.Close();
	}
}

TCK_W_Client_IoTDialog::~TCK_W_Client_IoTDialog()
{
    //(*Destroy(TCK_W_Client_IoTDialog)
    //*)
}

extern char g_iot_server_ip[40];
extern unsigned short g_web_server_port;
extern unsigned short g_web_alive_time;
extern unsigned short g_view_server_port;
extern unsigned short g_view_alive_time;

void TCK_W_Client_IoTDialog::OnButton1Click(wxCommandEvent& event)
{
    wxString ip = g_iot_server_ip;
    if(ip == wxEmptyString)
        ip = _("127.0.0.1");
    wxString uname = TextCtrl2->GetValue();
    if(uname == wxEmptyString)
    {
        wxMessageBox(_("Please enter user name !"), _("Prompt"));
        return;
    }
    wxString pwd = TextCtrl3->GetValue();
    if(pwd == wxEmptyString)
    {
        wxMessageBox(_("Please enter password !"), _("Prompt"));
        return;
    }
    if(!g_tcp_web)
    {
    	g_tcp_web = new TcpClientWeb(g_web_alive_time);
    }
    int port_web = g_web_server_port;
    if(port_web == 0)
		port_web = 9100;
    if(!g_tcp_web->Connect(ip.ToStdString().c_str(), port_web, uname.ToStdString().c_str(), pwd.ToStdString().c_str()))
    {
        wxMessageBox(wxString::Format(_("Connection to server <web> [%d] failed !"), port_web), _("Prompt"));
        return;
    }
    /*if(!g_tcp_view)
    {
    	g_tcp_view = new TcpClientView(g_view_alive_time);
    }
    int port_view = g_view_server_port;
    if(port_view == 0)
		port_view = 9200;
	if(!g_tcp_view->Connect(ip.ToStdString().c_str(), port_view))
	{
		wxMessageBox(wxString::Format(_("Connection to server <view> [%d] failed !"), port_view), _("Prompt"));
		return;
	}*/
    Button1->Disable();
}

void TCK_W_Client_IoTDialog::OnLoginReply(wxCommandEvent& event)
{
    int ret = event.GetInt();
    if(ret == 0)
    {
    	EndModal(wxID_OK);
    	return;
    }
    else if(ret == 1)
		wxMessageBox(_("User name or password error !"), _("Prompt"));
	else if(ret == 2)
		wxMessageBox(_("Expiry of validity period !"), _("Prompt"));
	else
		wxMessageBox(_("Login failed !"), _("Prompt"));
	Button1->Enable();
}
