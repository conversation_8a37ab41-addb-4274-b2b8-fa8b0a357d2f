#ifndef WX_CURVE_H
#define WX_CURVE_H

#include <wx/wx.h>
#include <wx/panel.h>
#include <wx/dcmemory.h>
#include <wx/bitmap.h>
#include <vector>

#include "yblib.h"

///简单功能的曲线图
#define SIMPLE_CURVE

#ifdef SIMPLE_CURVE
typedef struct
{
	unsigned short dat;		///DA突变点
	unsigned short adt;		///AD突变点
	unsigned short dab;		///DA饱和点
	unsigned short adb;		///AD饱和点
	unsigned short daj;		///DA基准
	unsigned short adj;		///AD基准
	unsigned short k;		///斜率
	//传感器排列对应关系
	unsigned short horVer;		///0=水平传感器，1=垂直传感器，2=禁用
	unsigned long long ropes;	///按bit位对应绳关系
} CurveSensorParam;
class Interface ///接口类
{
  public:
	Interface(){};
    virtual ~Interface(){};
	/* 函数说明：曲线实时绘图用接口 */
	virtual void CurveRealtimeInit(unsigned long pt){}
	virtual void CurveRealtimeDraw(SmartPtr<char>& sp){}
	/* 函数说明：曲线实时图像重构用接口 */
	virtual void CurveReconstructionSetJtData(void *jt){}
};
#else
#define CurveSensorParam SlaveArm7::SensorParam
#endif

///最大支持曲线数，需设置为8的整数倍
#define MAX_CURVE_COUNT 128

///内存块大小
#define CURVE_BUFFER_LEN 256


class wxCurve : public EventBase, public Interface, public wxEvtHandler
{
	public:
		//消息事件
		enum EvtType
		{
			Evt_Init = 0,			///曲线图--初始化	（处理）
			Evt_Draw = 1,		///曲线图--画图		（处理）
			Evt_JtBsqNum = 2,	///绘制接头/抱索器编号
			Evt_JtReconstruction = 3	///图像重构用
			//Evt_Mouse			///鼠标事件			（发起）
		};
		/*/消息子事件
		enum EvtSubType
		{
			Evt_Mouse_Move,				///鼠标--移动
			Evt_Mouse_Left_DClick,		///鼠标--双击左键
			Evt_Mouse_Right_DClick		///鼠标--双击右键
		};*/

		//消息数据体
		/* 没有子事件的不使用此结构体
			Evt_Init: 不需要事件数据体
			Evt_Draw:
				struct{
					int realPoint;
					unsigned short pointCount;
					unsigned short sensorCount;
					char data[pointCount][sensorCount];
					char pvPoints[];		///峰谷点集，可有可没有，有的话，长度与data一样
				};
			Evt_JtBsqNum:
				struct{
					short type;		///0=接头，1=抱索器
					char num[30];
					unsigned long start;
					unsigned long end;
				};
		   有子事件的使用，Evt_Mouse
		*/
		/*typedef struct
		{
			EvtSubType type;
			wxCurve *obj;
			int pixel;
			int value;
		} EvtData;*/

		typedef struct
		{
			unsigned char f;
			wxPoint pt;
		} pvPoint;

		typedef struct
		{
			unsigned long p_x;
			unsigned long v_x;
			unsigned short p_y;
			unsigned short v_y;
		} RtReconstructionPV;
		typedef struct
		{
			int ss;
			unsigned long pos;
			float val;
		} RtReconstructionDT;
		typedef struct
		{
			bool isDraw;
			unsigned long start;
			unsigned long end;
			int lv;
		} RtReconstructionJT;
		typedef struct
		{
			wxString num;
			unsigned long start;
			unsigned long end;
		} RtReconstructionJTNum;

    public:
        wxCurve(wxWindow* parent,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
        ~wxCurve();
        bool IsInit();
		///画曲线使用指定背景图，及画坐标轴线所使用的画笔、画坐标值所用颜色、画虚线网格所用颜色
		void SetBGbmp(const wxBitmap *bmp, const wxPen *pen, const wxColour *color, const wxColour *wgColor);
        ///初始化
        void Initial(int xMinValue, int xMaxValue, int yMinValue, int yMaxValue,
                     unsigned short xSplit, unsigned short ySplit,
					 wxString xUnitName, wxString yUnitName,
					 float xLabelZoom = 1.0, float yLabelZoom = 1.0,
					 unsigned short xLabelPrec = 0, unsigned short yLabelPrec = 0,
					 bool hasCoordX = true, bool hasCoordY = true,
					 bool hasDotlineX = true, bool hasDotlineY = true,
                     int fontSize = 8, int curveWidth = 1,
                     bool screenHV = false, bool isLayering = false,
					 bool isRealtime = true, unsigned long long isMutilGP = 0,
					 char isCombin = 0, char isHorVer = 0,
					 char (*wilfullySelect)[MAX_CURVE_COUNT/8] = NULL,
					 const CurveSensorParam *ssParam = NULL);
		void Initial(int xMinValue, int xMaxValue, float xLabelZoom = 1.0, bool isRealtime = true);
		//void Initial(int xMinValue, int xMaxValue, int yMinValue, int yMaxValue, bool isLayering = false);
		void Initial(int xMinValue, int xMaxValue, int yMinValue, int yMaxValue, bool isLayering = false, bool isFirstInit = true, float yLabelZoom = 1.0);
        void Initial(bool isFirstInit = false);
		void ReSize(const wxSize& size);
		wxSize GetSize();
		void Show();
		void Hide();
		wxPanel *GetPanel();
		void GetPixelInfo(unsigned int *xOffset, unsigned int *xLenth, float *xUnit, unsigned int *yOffset, unsigned int *yLenth, float *yUnit);
		void GetValueXY(int *xMin, int *xMax, int *yMin, int *yMax);
		void SetValueXY(int xMin, int xMax, int yMin, int yMax, bool isUpdate=true);

		///设置曲线颜色
		void SetCurveColors(int cnt, const wxColour *colors);

		///获取图片，angle可旋转90、-90、180度
		wxBitmap GetBitmap(short angle=0);

		///根据像素点x坐标获取x数据值
        int GetPointsByPixel(int xPixel);
		float GetPosByPixel(int xPixel);
		///根据像素点y坐标获取曲线编号
		int GetNumByPixelY(int yPixel);

		///函数指针，外部提供的扩展画图函数
		typedef void (*ExtendDrawFunc)(void *func_obj, wxPaintDC& dc, unsigned int xPixel, unsigned int yPixel);

		///鼠标操作开启/关闭 -- 画线并显示各路传感器数值，传入func则由其自定义绘图，否则使用dataBuf默认绘制
        void SetMouseOptFlag(bool flag, void *obj, ExtendDrawFunc func, const unsigned char *dataBuf=NULL, unsigned short sscnt=0);

		///设置实时画图的外部扩展画图函数
        void SetExtendDraw(void *obj, ExtendDrawFunc func);

		///画曲线图（即时）
		void DrawCurve(const std::vector<float> *data, unsigned short ssCount, const std::vector<char> *pvPoints=NULL);

		///曲线移动、放大缩小
		// move: 0=不移动，-1=左翻一屏，1=右翻一屏
		// zoom: >0=X轴缩放，1.0=X轴不缩放，>1.0=放大，<1.0=缩小；<0=Y轴缩放，-1.0=不缩放，<-1.0=放大，>-1.0=缩小
		//   例: zoom=2.0，当前屏显10~20，屏显长度=10，放大2倍，结果屏显12.5~17.5，屏显长度=5
		//	 例: zoom=0.5，当前屏显10~20，屏显长度=10，缩小2倍，结果屏显5~25，屏显长度=20
		void DoMoveAndZoom(int move, float zoom);

		///数据文件画图
		/* 参数：
			filePath：文件完整路径，若为NULL，则弹出文件选择对话框
			splitIdx：文件分块，指定读取第N块，从0起
			splitCnt：文件分块，指定分块块数，若不分块则置0
			xOffset：X轴起始偏移量，单位：米
			xMin,xMax: 指定初始显示段（相当于通过放大缩小及左右移动来实现）, 单位：米
			ssCount：获取文件传感器路数
			sampleStep : 获取文件采样步长，单位：米/点
		   返回值：
			0 = 成功
			1 = 失败 -- 文件不存在
			2 = 失败 -- 非数据文件
		*/
		int DrawCurveByFile(const char *filePath, int splitIdx, int splitCnt, float xOffset, int xMin=0, int xMax=0, unsigned short *ssCount=NULL, float *sampleStep=NULL);
		///从文件中读取指定部分画图
		int DrawCurveByFile(const char *filePath, float startPos, float endPos);

		///设置选择集（按位置位）
		// flag: 0=初始化，1=读取，2=设置，3=清除
		// flag=0时，idx=0则全部初始化置位0，idx>0则初始化idx个置1
		// isRefresh：是否刷新重绘曲线，flag=1读取时此参数无效
		bool SetWilfullySelect(int idx, char flag, char isRefresh=0);

		///重绘曲线（即时）
		void ReDrawCurve(bool screenHV=false, bool isLayering=false, unsigned long long isMutilGP=0, char isCombin=0, char isHorVer=0);

#ifndef SIMPLE_CURVE
		///曲线转换（即时）
		// way：转换方法
		//  0=原始，1=低通滤波，2=捻距基准差，3=捻距基准差(前后)，4=锐化(2阶)，5=锐化(3阶)
		//	6=加权均值滤波，7=加权均值滤波差，8=减均值基准
		// 注：原始曲线为使用 DrawCurveByFile 加载的数据
		// njCnt：捻距长度，单位：点，计算公式：(N * DM) / (SS * 1000)，N为捻距系数，DM为直径(mm)，SS为采样步长(点/米)
		// qPole、hPole：前磁极、后磁极长度，单位：点
		// isResetZoom：是否重置缩放比例
		void ConvertCurve(char way, unsigned int njCnt, unsigned int qPole, unsigned int hPole, bool isResetZoom=false);
		///曲线转换（即时）
		// way：转换方法
		//  0=加权系数 K 按柔性缠绕摩擦力，1=加权系数 K 按余弦函数分布规律
		// 注：原始曲线为使用 DrawCurveByFile 加载的数据
		// nD：n倍直径；dm：直径，单位 mm；step：采样步长，单位 mm/点
		// qPole、hPole：前磁极、后磁极长度，单位：mm；
		// u：way=0时为摩擦力系数
		// isResetZoom：是否重置缩放比例
		void ConvertCurve_K(char way, int nD, int dm, float step, int qPole, int hPole, float u, bool isResetZoom=false);

		///曲线绘制峰谷点（即时）
		// ogham：峰谷落差过滤值，数组长度不得小于sscnt，为NULL则默认使用：(最大值-最小值)/3
		// limit：峰谷绝对值过滤，数组长度不得小于sscnt，为NULL则不过滤
		void DrawPeakValleyPoints(int *ogham, int *limit, int dbgIdx=-1);
#endif

		///设置要绘制的接头/抱索器编号（实时）
		/// type: 0=接头编号，1=抱索器编号
		void SetJtBsqNum(short type, const char *num, unsigned long start, unsigned long end);

		///设置即时画图，均线、上阈值线、下阈值线、左区域线、右区域线
		//void SetLines(bool avg=false, float up=0.0, float down=0.0, unsigned int left=0, unsigned int right=0);
		///设置即时画图,均线、上阀值线、下阀值线、上门限、下门限、左区域线、右区域线
		void SetLines(bool avg=false, float up=0.0, float down=0.0, float thresholdup=0.0, float thresholddown=0.0, unsigned int left=0, unsigned int right=0);

		///图像重构
		bool Initial_Image_Reconstruction(bool isOpen, int xMinValue=0, int xMaxValue=100000, float xLabelZoom=0.001, int fontSize=8, int threshold=100, bool peakValley=false, int dis30=500, int dis150=2000, float jtCoeff=3, int ropeCnt=80, const CurveSensorParam *ssParam = NULL);
		void Init_Image_Reconstruction(unsigned long pt);

    public:
        void OnDrawCurve(void *, unsigned int);
		void OnDrawCurveSP(SmartPtr<char>& sp);
        void DoInitial(void *, unsigned int);
		void OnJtBsqNumSP(SmartPtr<char>& sp);
		const std::vector<unsigned char> *GetSrcData();
		const std::vector<float> *GetData();

	public:
		/* 函数说明：曲线实时绘图用接口 */
		virtual void CurveRealtimeInit(unsigned long pt);
		virtual void CurveRealtimeDraw(SmartPtr<char>& sp);
		/* 函数说明：曲线实时图像重构用接口 */
		virtual void CurveReconstructionSetJtData(void *jt);

		//外部调用的实时绘图（不使用绘图线程）
		void ExtendDrawCurve(const std::vector<float> *data, unsigned short ssCount);

    private:
        void OnPaint(wxPaintEvent& event);
		void OnPaint__(wxPaintEvent& event);
        bool DrawCoord(unsigned int xPage);
        void DrawCurve(const unsigned char *data, unsigned short ssCount, unsigned int ptCount, const unsigned char *pvPoints=NULL);
		void Draw_Image_Reconstruction(const unsigned char *data, unsigned short ssCount, unsigned int ptCount);
		void DrawJt_Image_Reconstruction(RtReconstructionJT *jt);
		void DrawJtBsqNum(short type, const char *num, unsigned long start, unsigned long end);
		void DrawJtBsqNum_Image_Reconstruction(short type, const char *num, unsigned long start, unsigned long end);
		void OnMouseWheel(wxMouseEvent& event);
		void OnLeftDown(wxMouseEvent& event);
        void OnLeftUp(wxMouseEvent& event);
		void OnRightDown(wxMouseEvent& event);
        void OnRightUp(wxMouseEvent& event);
        void OnMouseMove(wxMouseEvent& event);
		void OnMouseWheel__(wxMouseEvent& event);
		void OnLeftDown__(wxMouseEvent& event);
        void OnLeftUp__(wxMouseEvent& event);
		void OnRightDown__(wxMouseEvent& event);
        void OnRightUp__(wxMouseEvent& event);
        void OnMouseMove__(wxMouseEvent& event);

        void xZoomIn();
        void xZoomOut();
        void yZoomIn();
        void yZoomOut();

    private:
		wxWindow *m_parent;
		wxPanel *m_panel;
		///内存池ID
		static int g_MemPoolID;
        ///画曲线使用的内存DC（内存DC中画曲线后，再粘贴置窗口，这样可防止闪烁）
        wxMemoryDC m_MemDC;
        ///画曲线使用的位图缓存
        wxBitmap m_BitMap;
        ///是否初始化，未初始化则后续画图动作无效
        bool m_isInit;
		///x轴原始起止值
        int m_xMinValue;
        int m_xMaxValue;
        ///y轴原始起止值
        int m_yMinValue;
        int m_yMaxValue;
		///x、y轴分割线
        unsigned short m_xSplit;
        unsigned short m_ySplit;
		///x、y轴下标单位名称
        wxString m_xUnitName;
        wxString m_yUnitName;
		///x、y轴下标值缩放比例
        float m_xLabelZoom;
        float m_yLabelZoom;
		///记录x、y轴单位精度
        unsigned short m_xLabelPrec;
        unsigned short m_yLabelPrec;
		///是否画x、y轴坐标
        bool m_hasCoordX;
		bool m_hasCoordY;
		///是否画x、y轴虚线
		bool m_hasDotlineX;
		bool m_hasDotlineY;
		///画图字体
        int m_fontSize;
        ///曲线线宽
        int m_curveWidth;
        ///屏显方式，false=横屏，true=竖屏
        bool m_screenHV;
        ///多曲线是否均匀排布
        bool m_isLayering;
		///指定传入数据数组中，那些要画曲线图（bit位）
		char m_wilfullySelect[MAX_CURVE_COUNT/8];
        ///记录x、y轴一个点对应多少个像素
        float m_xPixelUnit;
        float m_yPixelUnit;
        ///记录x、y轴轴长像素点数
        unsigned int m_xPixelLenth;
        unsigned int m_yPixelLenth;
        ///记录x、y轴偏移量像素点数
        unsigned int m_xPixelOffset;
        unsigned int m_yPixelOffset;
        ///保存每页像素偏移量
        unsigned int m_pageOffset;
        ///保存翻页页数
        unsigned int m_pageNumber;
        ///保存点数累加对比剩余量（1像素对应多点时）
        unsigned int m_accumRemain;
        ///保存点数小数位累加量（1像素对应多点时）
        float m_accumDecimal;
        ///绘画曲线点集Buffer
        std::vector<wxPoint> m_pointVector[MAX_CURVE_COUNT];
		///绘画曲线特殊点(峰谷)点集Buffer
		std::vector<pvPoint> m_pvVector[MAX_CURVE_COUNT];
        ///保存上一次的接续点
        wxPoint m_lastPoint[MAX_CURVE_COUNT];
        ///默认字体大小为8时，单个字符的宽度和高度
        int m_fontSize8_width;
        int m_fontSize8_height;

		///是否多路分组，0=不分组(默认)，0x00000001~0xFFFFFFFF=按bit位，根据对应关系，指定分组
		unsigned long long m_isMutilGP;
		///是否实时画图
        bool m_isRealtime;
		///是否合并，0=不合并(默认)，1=合并
		char m_isCombin;
		///是否区分水平垂直，0=不区分(默认)，1=仅水平，2=仅垂直，3=水平+垂直
		char m_isHorVer;
		///(传感器)对应关系
		const CurveSensorParam *m_ssParam;

		///x轴起止值（缩放用）
        int m_xMin;
        int m_xMax;
        ///y轴起止值（缩放用）
        float m_yMin;
        float m_yMax;
		///鼠标左键or右键按下（0=未按下，1=左键按下，2=右键按下）
		short m_leftOrRight;
		///记录鼠标位置
        wxPoint m_xLogicalPos;
        wxPoint m_yLogicalPos;

		///记录即时画图数据、特殊点，曲线缩放用
		std::vector<float> m_data[MAX_CURVE_COUNT];
		std::vector<char> m_pvData[MAX_CURVE_COUNT];
		///记录即时画图数据路数
		unsigned short m_ssCount;
		///记录即时画图分层显示移动量
		float m_layerOffsetPixel[MAX_CURVE_COUNT];
		///记录即时画图y均值
		float m_yAvgValue[MAX_CURVE_COUNT];

		///记录读文件的数据、Y轴设定最大最小值、Y轴数据真实最大最小值
		std::vector<unsigned char> m_srcData[MAX_CURVE_COUNT];
		int m_yMinValue_src;
        int m_yMaxValue_src;
		int m_yMinValue_real;
		int m_yMaxValue_real;

		///实时外部扩展画图
		void *m_extendDrawFuncObj;
		ExtendDrawFunc m_extendDrawFunc;

		///鼠标画线用
		void *m_exDrawFuncObj;
		ExtendDrawFunc m_exDrawFunc;
		const unsigned char *m_dataBuf;
		unsigned short m_exDrawSSCnt;
		unsigned int m_curXPixel;
		unsigned int m_curYPixel;

		///画曲线使用指定背景图，及画坐标轴线所使用的画笔、画坐标值所用颜色、画虚线网格所用颜色
        wxBitmap m_bgBmp;
        wxPen m_bgPen;
        wxColour m_bgColor;
        wxColour m_wgColor;

		///保存画布大小
		wxSize m_size;

		///画曲线使用的画笔
		std::vector<wxPen> m_pens;
		wxPen m_pen_blue;
		wxPen m_pen_green;
		wxPen m_pen_black;

		///即时画图，均线、上阈值线、下阈值线、左区域线、右区域线
		bool m_line_avg;
		float m_line_up;
		float m_line_down;
		unsigned int m_line_left;
		unsigned int m_line_right;

		float m_line_up_threshold;
		float m_line_down_threshold;

		///曲线颜色
		int m_curve_color_cnt;
		wxColour *m_curve_colors;

		///图像重构（实时画图 -- 皮带）
		bool m_is_image_reconstruction;
		unsigned short m_threshold;
		bool m_peakValley;
		int m_dis30;
		int m_dis150;
		float m_jtCoeff;
		int m_ropeCnt;
		int m_pos0;
		unsigned long m_pointCount;
		unsigned long m_lastPtCount;
		unsigned long m_totalPoints;
		float m_mvPixRest;		//移动画图移动像素的剩余量
		int m_elePixOffset;		//移动画图元素图片像素偏移量
		wxSize m_reconstruction_size;
		std::vector<RtReconstructionDT> m_rtReconstruction_dt;		///实时延后判定用数据
		RtReconstructionPV m_rtReconstruction_pv[MAX_CURVE_COUNT];	///实时对比法判定用数据
		RtReconstructionJT m_jtData;
		std::vector<RtReconstructionJTNum> m_jtNum;
		unsigned short m_jtOgham[MAX_CURVE_COUNT];	///接头平均峰谷落差（每圈的）
		wxBitmap m_bmp_ele;
		wxBitmap m_bmp_brk;
		wxBitmap m_bmp_jt0;
		wxBitmap m_bmp_jt1;
		wxBitmap m_bmp_jt2;
		wxBitmap m_bmp_jt3;
		wxBitmap m_bmp_jt4;
		wxBitmap m_bmp_reconstruction;

	public:
		//实时画图用线程
		friend void RealTimeDrawCurveThread(void *arg, unsigned int arglen);
};

#endif // WX_CURVE_H
