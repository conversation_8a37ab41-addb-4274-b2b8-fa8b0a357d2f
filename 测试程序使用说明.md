# 接头拍照功能测试程序使用说明

## 1. 概述

本测试程序用于验证接头自动拍照系统的各个核心功能模块，包括摄像头连接、拍照控制、存储管理、图片查看等功能。

## 2. 编译环境要求

### 2.1 Windows 环境
- **编译器**: MinGW-w64 或 Visual Studio 2017+
- **wxWidgets**: 3.0+ 版本
- **海康威视SDK**: HCNetSDK
- **其他**: CMake 3.10+ (可选)

### 2.2 Linux 环境
- **编译器**: GCC 7.0+ 或 Clang 6.0+
- **wxWidgets**: 3.0+ 开发包
- **海康威视SDK**: Linux版本
- **依赖包**: build-essential, libwxgtk3.0-gtk3-dev

## 3. 编译步骤

### 3.1 Windows 编译
```batch
# 方法1: 使用批处理脚本
build_test.bat

# 方法2: 手动编译
g++ -std=c++11 -Wall -g `wx-config --cxxflags` -I./include -c *.cpp
g++ *.o -o JointCaptureTest.exe `wx-config --libs` -L./lib -lHCNetSDK
```

### 3.2 Linux 编译
```bash
# 方法1: 使用Makefile
make -f Makefile.test

# 方法2: 安装依赖后编译
sudo apt-get install libwxgtk3.0-gtk3-dev
make -f Makefile.test all

# 运行测试
make -f Makefile.test run
```

## 4. 测试前准备

### 4.1 配置文件准备
确保 `capture_config.ini` 文件存在并配置正确：

```ini
[Camera]
IP=*************          # 摄像头实际IP地址
Port=8000                 # 摄像头端口
Username=admin            # 登录用户名
Password=admin123         # 登录密码
Resolution=2560x1440      # 分辨率
FrameRate=30             # 帧率

[Capture]
CameraDistance=2.0        # 摄像头安装距离(米)
SafetyBuffer=0.5         # 安全缓冲距离(米)
PhotoQuality=95          # 照片质量(0-100)
AutoCaptureEnabled=true  # 是否启用自动拍照
PhotoRootPath=photos     # 照片存储根目录
```

### 4.2 网络环境
- 确保测试计算机与摄像头在同一网段
- 摄像头IP地址可以ping通
- 防火墙允许相关端口通信

### 4.3 目录权限
- 确保程序有权限在当前目录创建文件和文件夹
- 照片存储目录有读写权限

## 5. 测试功能说明

### 5.1 配置加载测试
- **功能**: 验证配置文件读取功能
- **测试内容**: 
  - 默认配置生成
  - 配置文件加载
  - 参数有效性验证
- **预期结果**: 显示所有配置参数

### 5.2 摄像头连接测试
- **功能**: 验证海康威视摄像头连接
- **测试内容**:
  - SDK初始化
  - 设备登录
  - 连接状态检查
- **预期结果**: 成功连接到指定摄像头

### 5.3 存储管理测试
- **功能**: 验证照片存储目录管理
- **测试内容**:
  - 目录自动创建
  - 路径生成规则
  - 文件命名规范
- **预期结果**: 创建正确的目录结构

### 5.4 拍照控制测试
- **功能**: 验证拍照控制逻辑
- **测试内容**:
  - 控制器初始化
  - 参数设置
  - 延迟计算
  - 模拟接头检测
- **预期结果**: 正确计算拍照时机

### 5.5 图片查看测试
- **功能**: 验证图片查看器功能
- **测试内容**:
  - 创建测试图片
  - 查看器界面显示
  - 缩放功能测试
- **预期结果**: 正常显示和操作图片

### 5.6 完整功能测试
- **功能**: 执行所有测试项目
- **测试内容**: 依次运行上述所有测试
- **预期结果**: 生成完整的测试报告

## 6. 测试结果解读

### 6.1 成功标识
- `✓ 测试通过`: 功能正常
- 绿色文字: 正常信息
- 详细信息: 显示具体参数和结果

### 6.2 失败标识
- `✗ 测试失败`: 功能异常
- 红色文字: 错误信息
- 详细信息: 显示错误原因

### 6.3 统计信息
- 总测试数: 执行的测试项目数量
- 通过测试: 成功的测试数量
- 失败测试: 失败的测试数量

## 7. 常见问题排除

### 7.1 编译错误
**问题**: 找不到头文件
**解决**: 检查wxWidgets和海康SDK路径配置

**问题**: 链接错误
**解决**: 确认库文件路径和版本匹配

### 7.2 摄像头连接失败
**问题**: 连接超时
**解决**: 
1. 检查网络连接
2. 确认IP地址正确
3. 检查防火墙设置

**问题**: 登录失败
**解决**:
1. 确认用户名密码
2. 检查摄像头用户权限
3. 尝试通过浏览器访问摄像头

### 7.3 目录创建失败
**问题**: 权限不足
**解决**: 
1. 以管理员权限运行
2. 检查目录权限设置
3. 更改存储路径到有权限的目录

### 7.4 图片查看失败
**问题**: 图片格式不支持
**解决**: 确认wxWidgets支持JPEG格式

**问题**: 内存不足
**解决**: 降低图片分辨率或质量

## 8. 测试报告示例

```
=== 接头拍照功能测试程序启动 ===
程序版本: v1.0
测试时间: 2024-01-15 14:30:25

>>> 开始测试: 配置文件加载
✓ 测试通过: 配置文件加载
  详细信息: 摄像头IP: *************, 端口: 8000, 用户名: admin, 安装距离: 2.0米, 照片质量: 95

>>> 开始测试: 摄像头连接
✓ 测试通过: 摄像头连接
  详细信息: 成功连接到摄像头 *************:8000

>>> 开始测试: 存储管理
✓ 测试通过: 存储管理
  详细信息: 目录: test_photos/2024-01-15/circle_1, 照片路径: test_photos/2024-01-15/circle_1/joint_001_20240115143025.jpg

>>> 开始测试: 拍照控制
✓ 测试通过: 拍照控制
  详细信息: 接头长度: 2.0米, 摄像头距离: 2.0米, 计算延迟: 1000毫秒

=== 完整功能测试结果 ===
总测试数: 4
通过测试: 4
失败测试: 0
✓ 所有测试通过！系统功能正常。
```

## 9. 注意事项

1. **安全性**: 测试过程中不会实际拍摄照片，避免对生产环境影响
2. **网络**: 确保测试环境网络稳定
3. **权限**: 某些功能需要管理员权限
4. **清理**: 测试完成后会自动清理临时文件
5. **日志**: 保存测试日志用于问题分析

## 10. 技术支持

如遇到问题，请提供：
- 测试程序版本
- 完整的错误日志
- 系统环境信息
- 配置文件内容
