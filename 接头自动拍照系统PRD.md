# 接头自动拍照系统产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
在皮带输送带检测系统中，需要对检测到的接头进行自动拍照记录，以便后续分析和维护。当前系统已能检测到接头位置，需要在此基础上增加自动拍照功能。

### 1.2 项目目标
- 实现接头检测时的自动拍照功能
- 建立完整的图片存储和管理体系
- 提供便捷的图片查看功能

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 自动拍照触发
- **触发事件**: OnFindJtOrBsq事件
- **触发时机**: 接头末端经过MRT设备时
- **计算要素**: 
  - 接头长度: endPos - startPos (1-3米)
  - 皮带速度: stw->run_data.speed (<4m/s)
  - 摄像头安装位置偏移量

#### 2.1.2 摄像头安装位置计算
```
最佳安装距离 = 接头长度/2 + 安全缓冲距离
拍摄延迟时间 = 安装距离 / 皮带速度
```

#### 2.1.3 拍摄时机计算
```
拍摄触发时间 = 接头检测时间 + 拍摄延迟时间
```

### 2.2 图片存储管理

#### 2.2.1 目录结构设计
```
/photos/
├── 2024-01-15/
│   ├── circle_1/
│   │   ├── joint_001_20240115143022.jpg
│   │   ├── joint_002_20240115143156.jpg
│   │   └── ...
│   ├── circle_2/
│   │   └── ...
│   └── ...
├── 2024-01-16/
│   └── ...
```

#### 2.2.2 文件命名规则
- 格式: `joint_[接头编号]_[时间戳].jpg`
- 接头编号: 3位数字，如001, 002
- 时间戳: YYYYMMDDHHMMSS格式

### 2.3 用户交互功能

#### 2.3.1 图片查看
- 在BeltQueryJoint页面右键点击接头号
- 自动打开该接头当日最新拍摄的图片
- 支持图片缩放、旋转等基本操作

## 3. 技术实现方案

### 3.1 系统架构设计

#### 3.1.1 模块划分
1. **拍照控制模块** (JointCaptureController)
   - 负责拍照时机计算和触发
   - 管理拍照任务队列

2. **摄像头管理模块** (CameraManager)
   - 封装海康威视SDK
   - 提供统一的拍照接口

3. **图片存储模块** (PhotoStorageManager)
   - 管理目录结构创建
   - 处理文件命名和存储

4. **图片查看模块** (PhotoViewer)
   - 提供图片查看界面
   - 集成到BeltQueryJoint页面

### 3.2 关键算法

#### 3.2.1 摄像头安装位置计算
```cpp
float CalculateCameraDistance(float jointLength) {
    const float SAFETY_BUFFER = 0.5f; // 安全缓冲距离(米)
    return jointLength / 2.0f + SAFETY_BUFFER;
}
```

#### 3.2.2 拍摄延迟计算
```cpp
int CalculateCaptureDelay(float cameraDistance, float beltSpeed) {
    if (beltSpeed <= 0) return 0;
    return (int)((cameraDistance / beltSpeed) * 1000); // 转换为毫秒
}
```

### 3.3 接口设计

#### 3.3.1 拍照控制接口
```cpp
class JointCaptureController {
public:
    void OnJointDetected(const JointInfo& jointInfo);
    void SetCameraDistance(float distance);
    void EnableAutoCapture(bool enable);
    
private:
    void ScheduleCapture(const JointInfo& jointInfo, int delayMs);
    void ExecuteCapture(const JointInfo& jointInfo);
};
```

#### 3.3.2 摄像头管理接口
```cpp
class CameraManager {
public:
    bool Initialize(const char* ip, int port, const char* user, const char* password);
    bool CapturePhoto(const char* filePath);
    void Cleanup();
    
private:
    LONG m_userID;
    bool m_initialized;
};
```

## 4. 实施计划

### 4.1 开发阶段
1. **阶段1**: 摄像头管理模块开发
2. **阶段2**: 拍照控制模块开发  
3. **阶段3**: 图片存储模块开发
4. **阶段4**: 用户界面集成
5. **阶段5**: 测试和优化

### 4.2 技术风险
- 海康威视SDK集成复杂度
- 拍摄时机精度控制
- 多线程同步问题

## 5. 验收标准

### 5.1 功能验收
- [ ] 接头检测时能自动触发拍照
- [ ] 拍摄的图片包含完整接头
- [ ] 图片按规定格式存储
- [ ] 用户能正常查看接头图片

### 5.2 性能验收
- [ ] 拍照响应时间 < 500ms
- [ ] 图片质量清晰可辨识
- [ ] 系统运行稳定无崩溃

## 6. 配置参数

### 6.1 摄像头配置
```ini
[Camera]
IP=*************
Port=8000
Username=admin
Password=admin123
Resolution=2560x1440
FrameRate=30
```

### 6.2 拍照配置
```ini
[Capture]
CameraDistance=2.0
SafetyBuffer=0.5
PhotoQuality=95
AutoCaptureEnabled=true
```
