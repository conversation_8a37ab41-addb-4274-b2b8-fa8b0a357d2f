@echo off

cd tools

xgettext.exe -C --keyword=_ --from-code=gb2312 ../../TCK_W_Client_IoTApp.cpp --output-dir=../ --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../TCK_W_Client_IoTMain.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltJointCurve.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltCurve.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltYBRedoSelDate.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltQueryAlarm.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltReport.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltQueryJoint.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltQueryFlaw.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltQueryTear.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../AccountDialog.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltChart.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../MainFrame.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltFrame.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltQueryTearAbn.cpp --output-dir=../ -j --output=english.po
xgettext.exe -C --keyword=_ --from-code=gb2312 ../../BeltTearReport.cpp --output-dir=../ -j --output=english.po

pause

exit