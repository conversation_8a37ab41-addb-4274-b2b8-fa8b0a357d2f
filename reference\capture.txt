客户端抓图，本模块介绍的是客户端抓图，设备抓图或者预览抓图，抓取JPEG或者BMP图片保存在PC本地磁盘。
一、接口调用流程
流程图./flow.png

图中虚线框部分的模块是与预览模块相关，必须在启动预览后才能调用，这些模块之间是并列的关系，各自完成相应的功能。实时预览支持TCP、UDP、MULTICAST网络传输模式，应用层协议支持私有协议和RTSP协议，码流类型可以选择主码流、子码流、第三码流等。

初始化NET_DVR_Init接口在程序开始是调用，只需要调用一次。

用户注册即登录设备，调用NET_DVR_Login_V40接口，每一台设备只需要登录一次。

异常消息回调：可以调用NET_DVR_SetExceptionCallBack_V30设置异常消息回调，预览非阻塞方式判断预览连接是否成功或者预览过程中网络异常、重连，可以回调获取对应的异常消息类型：EXCEPTION_PREVIEW、EXCEPTION_RECONNECT、PREVIEW_RECONNECTSUCCESS等。

客户端抓图，有设备抓图和预览抓图两种方式：
##设备抓图##：SDK发送抓图命令给设备，设备抓取JPEG图片之后回传给SDK，需要设备支持且受到设备性能限制。相关接口：NET_DVR_CaptureJPEGPicture(直接存成图片文件)、NET_DVR_CaptureJPEGPicture_NEW(图片数据存放在缓冲区中)。设备抓图仅支持设备图片质量，无法设置码流类型，抓图码流类型由设备能力确定。
备注：
* 该接口用于设备的单帧数据捕获，并保存成JPEG图片文件。JPEG抓图功能或者抓图分辨率需要设备支持，如果不支持接口返回失败，错误号23或者29。
* 对于DVR、NVR设备，参数wPicQuality支持的分辨率值可通过NET_DVR_GetDeviceAbility获取能力集类型PIC_CAPTURE_ABILITY获取(NET_DVR_COMPRESSIONCFG_ABILITY)得到。
* 对接网络摄像机、门禁主机等设备，设备是否支持JPEG抓图功能或者支持的参数能力，可以通过设备能力集进行判断，对应设备JPEG抓图能力集(JpegCaptureAbility)，相关接口：NET_DVR_GetDeviceAbility，能力集类型：DEVICE_JPEG_CAP_ABILITY，节点：ManualCapture。
* wPicSize设为2抓取的图片分辨率是4CIF还是D1由设备决定，一般为4CIF(P制:704576/N制:704480)。

预览实时流解码有两种方式：
##方式一##：预览接口NET_DVR_RealPlay_V40中预览参数的播放窗口句柄（hPlayWnd）赋值为有效句柄，则由SDK自动实现解码显示功能。在初始化SDK和注册设备两步骤后，直接调用启动预览和停止预览接口即可。正常开启预览之后可以调用NET_DVR_RigisterDrawFun注册画图回调函数（仅Windows版本支持），回调获取窗口DC，然后用户可以自己在窗口表层绘图或者写字。如果预览的码流是音视频复合流，也可以调用声音预览控制相关接口实现打开或者关闭声音、客户端音量控制等功能，相关接口有：NET_DVR_OpenSound、NET_DVR_CloseSound、NET_DVR_OpenSoundShare、NET_DVR_CloseSoundShare、NET_DVR_Volume等。
##方式二##：预览接口NET_DVR_RealPlay_V40中预览参数的播放窗口句柄（hPlayWnd）可以设置为空值，直接设置回调函数，或者调用预览接口之后，通过NET_DVR_SetRealDataCallBack、NET_DVR_SetStandardDataCallBack设置回调函数，回调获取实时流数据（前两个接口设置的回调获取的是PS封装的码流，后者获取的是标准RTP封装的码流）之后用户后续自己处理，比如二进制流方式写入文件保存成录像或者调用播放库解码显示等操作。

##预览抓图##：预览时播放库从解码码流里面抓取图片，抓图分辨率跟码流分辨率一致，必须先启动预览，支持BMP和JPEG抓图模式。预览实时流解码使用“方式一”时，相关接口：NET_DVR_SetCapturePictureMode(设置抓图模式)、NET_DVR_CapturePictureBlock_New(阻塞抓图且数据存放在缓冲区中)、NET_DVR_CapturePictureBlock(阻塞抓图且直接存成文件)、NET_DVR_CapturePicture(非阻塞抓图且直接存成文件)。预览实时流解码使用“方式二”时，需要用户自己调用播放库SDK相关抓图接口进行抓图，具体请参考播放库编程指南。

需要结束预览时，调用NET_DVR_StopRealPlay。

退出程序时调用NET_DVR_Logout注销设备，每一台设备调用一次。最后调用NET_DVR_Cleanup释放SDK所有资源。

实时预览时，设备通道预览连接数有限，不同的设备限制不尽相同，由设备性能决定。

c++ demo
下面示例代码为客户端抓图两种方式的C++示例代码，仅供参考。
#include <stdio.h>
#include <iostream>
#include "Windows.h"
#include "HCNetSDK.h"
#include <time.h>
using namespace std;

typedef HWND (WINAPI *PROCGETCONSOLEWINDOW)();
PROCGETCONSOLEWINDOW GetConsoleWindowAPI;

void CALLBACK g_ExceptionCallBack(DWORD dwType, LONG lUserID, LONG lHandle, void *pUser)
{
    char tempbuf[256] = {0};
    switch(dwType) 
    {
    case EXCEPTION_RECONNECT:    //预览时重连
        printf("----------reconnect--------%d\n", time(NULL));
    break;
	default:
    break;
    }
}

void main() {

  //---------------------------------------
  // 初始化
  NET_DVR_Init();
  //设置连接时间与重连时间
  NET_DVR_SetConnectTime(2000, 1);
  NET_DVR_SetReconnect(10000, true);
  
  //---------------------------------------
  //设置异常消息回调函数
  NET_DVR_SetExceptionCallBack_V30(0, NULL,g_ExceptionCallBack, NULL);

  //---------------------------------------
  // 获取控制台窗口句柄
  HMODULE hKernel32 = GetModuleHandle("kernel32");
  GetConsoleWindowAPI = (PROCGETCONSOLEWINDOW)GetProcAddress(hKernel32,"GetConsoleWindow");

  //---------------------------------------
  // 注册设备
  LONG lUserID;

  //登录参数，包括设备地址、登录用户、密码等
  NET_DVR_USER_LOGIN_INFO struLoginInfo = {0};
  struLoginInfo.bUseAsynLogin = 0; //同步登录方式
  strcpy(struLoginInfo.sDeviceAddress, "**********"); //设备IP地址
  struLoginInfo.wPort = 8000; //设备服务端口
  strcpy(struLoginInfo.sUserName, "admin"); //设备登录用户名
  strcpy(struLoginInfo.sPassword, "abcd1234"); //设备登录密码
  
  //设备信息, 输出参数
  NET_DVR_DEVICEINFO_V40 struDeviceInfoV40 = {0};

  lUserID = NET_DVR_Login_V40(&struLoginInfo, &struDeviceInfoV40);
  if (lUserID < 0)
  {
      printf("Login failed, error code: %d\n", NET_DVR_GetLastError());
      NET_DVR_Cleanup();
      return;
  }
    else{//设备抓图
        sprintf(PicName,"E:\Capture.jpg");
        NET_DVR_JPEGPARA JpgPara = {0};
        JpgPara.wPicSize = 5 ;
		JpgPara.wPicQuality = 0 ;
        NET_DVR_CaptureJPEGPicture(lUserID, struPlayInfo.lChannel, &JpgPara, PicName)
    }


  //---------------------------------------
  //启动预览并设置回调数据流
  LONG lRealPlayHandle;
  HWND hWnd = GetConsoleWindowAPI();     //获取窗口句柄
  NET_DVR_PREVIEWINFO struPlayInfo = {0};
  struPlayInfo.hPlayWnd = hWnd;         //需要SDK解码时句柄设为有效值，仅取流不解码时可设为空
  struPlayInfo.lChannel     = 1;       //预览通道号
  struPlayInfo.dwStreamType = 0;       //0-主码流，1-子码流，2-码流3，3-码流4，以此类推
  struPlayInfo.dwLinkMode   = 0;       //0- TCP方式，1- UDP方式，2- 多播方式，3- RTP方式，4-RTP/RTSP，5-RSTP/HTTP
  struPlayInfo.bBlocked     = 1;       //0- 非阻塞取流，1- 阻塞取流

  lRealPlayHandle = NET_DVR_RealPlay_V40(lUserID, &struPlayInfo, NULL, NULL);
  if (lRealPlayHandle < 0)
  {
      printf("NET_DVR_RealPlay_V40 error\n");
	  NET_DVR_Logout(lUserID);
      NET_DVR_Cleanup();
      return;
  }
    else{
		sprintf(PicName,"E:\RealPlay.bmp");
		if(NET_DVR_CapturePicture(lRealPlayHandle,PicName))
		{
			printf("抓图成功!");
		}
    }

  Sleep(10000);
  //---------------------------------------
  //关闭预览
  NET_DVR_StopRealPlay(lRealPlayHandle);
  //注销用户
  NET_DVR_Logout(lUserID);
  //释放SDK资源
  NET_DVR_Cleanup();

  return;
}
