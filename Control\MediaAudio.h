#ifndef MEDIAAUDIO_H
#define MEDIAAUDIO_H

#include <wx/mediactrl.h>

class MediaAudio : public wxMediaCtrl
{
    public:
        MediaAudio(wxWindow* parent, wxString filePath);
        virtual ~MediaAudio();
        void myPlay(bool isContinue=true);
        void myStop();
    protected:
        void OnMediaStop(wxMediaEvent& event);
    private:
        bool isStopContinuePlay;
};

#endif // MEDIAAUDIO_H
