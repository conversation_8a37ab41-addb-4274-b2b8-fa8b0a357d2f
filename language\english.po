# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-11 16:07+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../../TCK_W_Client_IoTApp.cpp:316
msgid "轻微"
msgstr "Slight"

#: ../../TCK_W_Client_IoTApp.cpp:316
msgid "轻度"
msgstr "Minor"

#: ../../TCK_W_Client_IoTApp.cpp:316
msgid "中度"
msgstr "Medium"

#: ../../TCK_W_Client_IoTApp.cpp:316
msgid "较重"
msgstr "Serious"

#: ../../TCK_W_Client_IoTApp.cpp:316
msgid "严重"
msgstr "Severe"

#: ../../TCK_W_Client_IoTApp.cpp:316
msgid "超限"
msgstr "OverLimit"

#: ../../TCK_W_Client_IoTApp.cpp:336
msgid "预警"
msgstr "Warn"

#: ../../TCK_W_Client_IoTApp.cpp:336 ../../BeltQueryTear.cpp:150
#: ../../BeltTearReport.cpp:463
msgid "报警"
msgstr "Alarm"

#: ../../TCK_W_Client_IoTApp.cpp:337
msgid "黄色"
msgstr "Yellow"

#: ../../TCK_W_Client_IoTApp.cpp:337
msgid "红色"
msgstr "Red"

#: ../../TCK_W_Client_IoTApp.cpp:340
msgid "通信"
msgstr "Comm"

#: ../../TCK_W_Client_IoTApp.cpp:340
msgid "传感器"
msgstr "Sensor"

#: ../../TCK_W_Client_IoTApp.cpp:340
msgid "编码器"
msgstr "Encoder"

#: ../../TCK_W_Client_IoTApp.cpp:394
msgid "服务进程启动失败！"
msgstr "Service process startup failed !"

#: ../../TCK_W_Client_IoTApp.cpp:394 ../../TCK_W_Client_IoTApp.cpp:481
#: ../../TCK_W_Client_IoTApp.cpp:499 ../../BeltQueryAlarm.cpp:257
#: ../../BeltQueryAlarm.cpp:259 ../../BeltReport.cpp:497
#: ../../BeltReport.cpp:603 ../../BeltReport.cpp:688 ../../BeltReport.cpp:789
#: ../../BeltQueryJoint.cpp:495 ../../BeltQueryJoint.cpp:610
#: ../../BeltQueryJoint.cpp:687 ../../BeltQueryJoint.cpp:691
#: ../../BeltQueryJoint.cpp:695 ../../BeltQueryJoint.cpp:699
#: ../../BeltQueryFlaw.cpp:479 ../../BeltQueryFlaw.cpp:607
#: ../../BeltQueryFlaw.cpp:615 ../../BeltQueryFlaw.cpp:621
#: ../../BeltQueryFlaw.cpp:623 ../../BeltQueryTear.cpp:212
#: ../../BeltQueryTear.cpp:241 ../../BeltQueryTear.cpp:287
#: ../../AccountDialog.cpp:166 ../../AccountDialog.cpp:168
#: ../../AccountDialog.cpp:170 ../../AccountDialog.cpp:219
#: ../../AccountDialog.cpp:221 ../../AccountDialog.cpp:223
#: ../../AccountDialog.cpp:258 ../../AccountDialog.cpp:264
#: ../../AccountDialog.cpp:270 ../../AccountDialog.cpp:295
#: ../../AccountDialog.cpp:301 ../../AccountDialog.cpp:321
#: ../../AccountDialog.cpp:343 ../../AccountDialog.cpp:365
msgid "提示"
msgstr "Tips"

#: ../../TCK_W_Client_IoTApp.cpp:481
msgid "本地语言环境读取失败！"
msgstr "Local locale reading failed !"

#: ../../TCK_W_Client_IoTApp.cpp:488
msgid "在线"
msgstr "On-line"

#: ../../TCK_W_Client_IoTApp.cpp:489
msgid "索道"
msgstr "Cableway"

#: ../../TCK_W_Client_IoTApp.cpp:490
msgid "猴车"
msgstr "Monkey Car"

#: ../../TCK_W_Client_IoTApp.cpp:491
msgid "电梯"
msgstr "Elevator"

#: ../../TCK_W_Client_IoTApp.cpp:492
msgid "便携"
msgstr "Portable"

#: ../../TCK_W_Client_IoTApp.cpp:493
msgid "皮带"
msgstr "Belt"

#: ../../TCK_W_Client_IoTApp.cpp:499
msgid "已经运行了一个实例！"
msgstr "An instance has already been run !"

#: ../../BeltJointCurve.cpp:24
msgid "接头波形"
msgstr "Joint Wave Form"

#: ../../BeltJointCurve.cpp:26
msgid "样本"
msgstr "Sample"

#: ../../BeltJointCurve.cpp:27
msgid "最新"
msgstr "Newest"

#: ../../BeltYBRedoSelDate.cpp:24
msgid "样本重做"
msgstr "Sample redo"

#: ../../BeltYBRedoSelDate.cpp:28
msgid "注意：请选择修补接头之后的日期\n（不包含修补当日）来作为样本日，\n以保证样本数据是基于修补之后的。\n（推荐选择修补日后的第2天）"
msgstr "Note: Please select the date after the\nrepair of the joint (excluding the repair\ndate) as the sample date to ensure\nthat the sample data is based on the\nrepair date. (The second day after the\nrepair date is recommended)"

#: ../../BeltYBRedoSelDate.cpp:31
msgid "确定提交"
msgstr "Confirm"

#: ../../BeltQueryAlarm.cpp:45 ../../BeltQueryAlarm.cpp:61
#: ../../BeltQueryAlarm.cpp:76 ../../BeltQueryTearAbn.cpp:55
msgid "报警时间"
msgstr "Alarm time"

#: ../../BeltQueryAlarm.cpp:46 ../../BeltReport.cpp:1184
msgid "损伤位置"
msgstr "Flaw Pos"

#: ../../BeltQueryAlarm.cpp:47 ../../BeltQueryAlarm.cpp:63
#: ../../BeltReport.cpp:1193 ../../BeltReport.cpp:1320
#: ../../BeltQueryFlaw.cpp:79
msgid "横向位置"
msgstr "Lateral Pos"

#: ../../BeltQueryAlarm.cpp:48 ../../BeltReport.cpp:1593
#: ../../BeltQueryFlaw.cpp:80
msgid "参考断芯数"
msgstr "Refer broken"

#: ../../BeltQueryAlarm.cpp:49
msgid "损伤程度"
msgstr "Flaw level"

#: ../../BeltQueryAlarm.cpp:50 ../../BeltQueryAlarm.cpp:65
msgid "报警等级"
msgstr "Alarm level"

#: ../../BeltQueryAlarm.cpp:62 ../../BeltReport.cpp:1686
msgid "接头编号"
msgstr "Joint No"

#: ../../BeltQueryAlarm.cpp:64 ../../BeltReport.cpp:1317
msgid "抽动量"
msgstr "Shifting"

#: ../../BeltQueryAlarm.cpp:77
msgid "设备名"
msgstr "Device Name"

#: ../../BeltQueryAlarm.cpp:80
msgid "非接头区损伤报警记录："
msgstr "Non joint area damage alarm record: "

#: ../../BeltQueryAlarm.cpp:83
msgid "接头抽动报警记录："
msgstr "Joint shifting alarm record: "

#: ../../BeltQueryAlarm.cpp:86
msgid "设备故障报警记录："
msgstr "Equipment fault alarm record: "

#: ../../BeltQueryAlarm.cpp:90
msgid "报警重置"
msgstr "Alarm Reset"

#: ../../BeltQueryAlarm.cpp:257
msgid "报警重置成功！"
msgstr "Alarm reset succeeded !"

#: ../../BeltQueryAlarm.cpp:259
#, c-format
msgid "报警重置失败！\n原因码：%d"
msgstr "Alarm reset failed !\nReason code : %d"

#: ../../BeltReport.cpp:122
msgid "检测报告"
msgstr "Inspection Report"

#: ../../BeltReport.cpp:127 ../../BeltTearReport.cpp:94
msgid "输送带信息"
msgstr "Belt Information"

#: ../../BeltReport.cpp:128 ../../BeltTearReport.cpp:95
msgid "报告基本信息"
msgstr "Report basic information"

#: ../../BeltReport.cpp:129 ../../BeltTearReport.cpp:96
msgid "报告编号："
msgstr "Report No.: "

#: ../../BeltReport.cpp:131 ../../BeltTearReport.cpp:98
msgid "报告标题："
msgstr "Report Title: "

#: ../../BeltReport.cpp:132
msgid "钢芯输送带MRT检测报告"
msgstr "Steel Core Conveyor Belt MRT Report"

#: ../../BeltReport.cpp:133 ../../BeltTearReport.cpp:100
msgid "检测设备："
msgstr "Equipment: "

#: ../../BeltReport.cpp:134 ../../BeltTearReport.cpp:101
msgid "TCK.W钢芯输送带在线实时自动监测系统"
msgstr "TCK.W Conveyer Belt Real-time Automatic Monitoring System"

#: ../../BeltReport.cpp:135 ../../BeltTearReport.cpp:102
msgid "输送带名："
msgstr "Belt Name: "

#: ../../BeltReport.cpp:137 ../../BeltTearReport.cpp:104
msgid "规格型号："
msgstr "Specification: "

#: ../../BeltReport.cpp:141
msgid "请选择报告日期："
msgstr "Please select report date: "

#: ../../BeltReport.cpp:142 ../../BeltTearReport.cpp:106
msgid "报告日期："
msgstr "Report date: "

#: ../../BeltReport.cpp:145 ../../BeltTearReport.cpp:109
msgid "制造商："
msgstr "Manufacturer: "

#: ../../BeltReport.cpp:147 ../../BeltTearReport.cpp:111
msgid "长度："
msgstr "Length: "

#: ../../BeltReport.cpp:150 ../../BeltTearReport.cpp:114
msgid "宽度："
msgstr "Width: "

#: ../../BeltReport.cpp:153 ../../BeltTearReport.cpp:117
msgid "钢芯直径："
msgstr "Steel core DM:"

#: ../../BeltReport.cpp:156 ../../BeltTearReport.cpp:120
msgid "用途："
msgstr "Purpose: "

#: ../../BeltReport.cpp:158 ../../BeltTearReport.cpp:122
msgid "钢芯数量："
msgstr "Number of core:"

#: ../../BeltReport.cpp:160 ../../BeltTearReport.cpp:124
msgid "拉伸强度："
msgstr "Tensile strength:"

#: ../../BeltReport.cpp:162 ../../BeltTearReport.cpp:126
msgid "上下覆盖层厚度："
msgstr "Overburden thickness:"

#: ../../BeltReport.cpp:165
msgid "报告版页"
msgstr "Report page"

#: ../../BeltReport.cpp:166
msgid "损伤序列表"
msgstr "Flaw Sequence Table"

#: ../../BeltReport.cpp:168
msgid "接头信息表"
msgstr "Joint Info Table"

#: ../../BeltReport.cpp:170
msgid "弱磁波形图"
msgstr "Waveform"

#: ../../BeltReport.cpp:172
msgid "参数选项"
msgstr "Parameter Options"

#: ../../BeltReport.cpp:174 ../../BeltQueryFlaw.cpp:100
msgid "不过滤"
msgstr "No filtering"

#: ../../BeltReport.cpp:175 ../../BeltQueryFlaw.cpp:101
msgid "断芯1以上"
msgstr "Broken above 1 core"

#: ../../BeltReport.cpp:176 ../../BeltQueryFlaw.cpp:102
msgid "轻度以上"
msgstr "Minor or above"

#: ../../BeltReport.cpp:177 ../../BeltQueryFlaw.cpp:103
msgid "中度以上"
msgstr "Medium or above"

#: ../../BeltReport.cpp:178 ../../BeltQueryFlaw.cpp:104
msgid "较重以上"
msgstr "Serious or above"

#: ../../BeltReport.cpp:179 ../../BeltQueryFlaw.cpp:105
msgid "严重以上"
msgstr "Severe or above"

#: ../../BeltReport.cpp:180 ../../BeltQueryFlaw.cpp:106
msgid "超限以上"
msgstr "OverLimit"

#: ../../BeltReport.cpp:181
msgid "损伤过滤："
msgstr "Flaw filtration: "

#: ../../BeltReport.cpp:182
msgid "过滤疑似干扰"
msgstr "Filtering interference"

#: ../../BeltReport.cpp:184 ../../BeltQueryJoint.cpp:136
msgid "时间跨度："
msgstr "Time span: "

#: ../../BeltReport.cpp:186 ../../BeltQueryJoint.cpp:139
msgid "一个月"
msgstr "One month"

#: ../../BeltReport.cpp:187 ../../BeltQueryJoint.cpp:140
msgid "一个季度"
msgstr "One season"

#: ../../BeltReport.cpp:188 ../../BeltQueryJoint.cpp:141
msgid "半年"
msgstr "Half a year"

#: ../../BeltReport.cpp:189 ../../BeltQueryJoint.cpp:142
msgid "一年"
msgstr "One year"

#: ../../BeltReport.cpp:190 ../../BeltQueryJoint.cpp:143
msgid "两年"
msgstr "Two years"

#: ../../BeltReport.cpp:191 ../../BeltQueryJoint.cpp:144
msgid "三年"
msgstr "Three years"

#: ../../BeltReport.cpp:192 ../../BeltQueryJoint.cpp:145
msgid "全部"
msgstr "Whole"

#: ../../BeltReport.cpp:193
msgid "波形图每页长度："
msgstr "Waveform length per page: "

#: ../../BeltReport.cpp:198 ../../BeltTearReport.cpp:131
msgid "生成报告"
msgstr "Generate"

#: ../../BeltReport.cpp:199 ../../BeltTearReport.cpp:132
msgid "安装/更换日期："
msgstr "Installation date: "

#: ../../BeltReport.cpp:201 ../../BeltTearReport.cpp:134
msgid "钢芯间距："
msgstr "Core spacing: "

#: ../../BeltReport.cpp:497
msgid "报告生成失败！\n原因：接头数据获取失败！"
msgstr "Report generation failed !\nCause: Failed to obtain connector data !"

#: ../../BeltReport.cpp:603
#, c-format
msgid "报告生成失败！\n原因：接头抽动数据获取失败！[%d]"
msgstr "Report generation failed !\nCause: Failed to obtain joint shift data ! [%d]"

#: ../../BeltReport.cpp:688
#, c-format
msgid "接头[%s]趋势图数据获取失败！[%d]"
msgstr "Joint[%s] trend chart data acquisition failed ! [%d]"

#: ../../BeltReport.cpp:789 ../../BeltQueryJoint.cpp:610
#: ../../BeltQueryFlaw.cpp:479
#, c-format
msgid "波形图数据获取失败！[%d]"
msgstr "Waveform data acquisition failed ! [%d]"

#: ../../BeltReport.cpp:810
msgid "轻微-继续使用"
msgstr "Slight - Continue to use"

#: ../../BeltReport.cpp:810
msgid "轻度-继续使用"
msgstr "Minor - Continue to use"

#: ../../BeltReport.cpp:810
msgid "中度-加强监测"
msgstr "Medium - Strengthening monitoring"

#: ../../BeltReport.cpp:810
msgid "较重-维修修补"
msgstr "Serious - Repair"

#: ../../BeltReport.cpp:810
msgid "严重-停机修补"
msgstr "Severe - Shutdown and repair"

#: ../../BeltReport.cpp:810
msgid "超限-停机更换或修补"
msgstr "Over Limit - Shutdown for replacement or repair"

#: ../../BeltReport.cpp:811
msgid "○"
msgstr "○"

#: ../../BeltReport.cpp:811
msgid "●"
msgstr "●"

#: ../../BeltReport.cpp:811
msgid "▲"
msgstr "▲"

#: ../../BeltReport.cpp:811
msgid "◆"
msgstr "◆"

#: ../../BeltReport.cpp:811
msgid "■"
msgstr "■"

#: ../../BeltReport.cpp:811
msgid "★"
msgstr "★"

#: ../../BeltReport.cpp:926 ../../MainFrame.cpp:191 ../../MainFrame.cpp:993
#: ../../BeltFrame.cpp:174 ../../BeltTearReport.cpp:284
msgid "微软雅黑"
msgstr "Arial"

#: ../../BeltReport.cpp:938 ../../BeltTearReport.cpp:296
msgid "报告编号： "
msgstr "Report No.: "

#: ../../BeltReport.cpp:945 ../../BeltTearReport.cpp:303
msgid "检测设备： "
msgstr "Equipment: "

#: ../../BeltReport.cpp:952 ../../BeltTearReport.cpp:310
msgid "输送带名称： "
msgstr "Belt Name: "

#: ../../BeltReport.cpp:958 ../../BeltTearReport.cpp:316
msgid "输送带规格： "
msgstr "Specification: "

#: ../../BeltReport.cpp:964 ../../BeltTearReport.cpp:322
msgid "报告日期： "
msgstr "Report date: "

#: ../../BeltReport.cpp:970
msgid "检 验 人： "
msgstr "Inspectors: "

#: ../../BeltReport.cpp:974
msgid "技 术 员： "
msgstr "Technician: "

#: ../../BeltReport.cpp:978
msgid "分区负责人： "
msgstr "Division leader: "

#: ../../BeltReport.cpp:982
msgid "区队负责人： "
msgstr "District team leader: "

#: ../../BeltReport.cpp:986
msgid "机电管理部： "
msgstr "Management: "

#: ../../BeltReport.cpp:990
msgid "机电总师： "
msgstr "Chief Engineer: "

#: ../../BeltReport.cpp:1017
msgid "MRT 检 测 主 报 告"
msgstr "MRT Inspection Main Report"

#: ../../BeltReport.cpp:1023 ../../BeltReport.cpp:1554
#: ../../BeltReport.cpp:1673 ../../BeltReport.cpp:1850
#: ../../BeltTearReport.cpp:353 ../../BeltTearReport.cpp:516
#, c-format
msgid "第 %d 页    共 %d 页"
msgstr "Page %d of %d"

#: ../../BeltReport.cpp:1032 ../../BeltTearReport.cpp:359
msgid "基本信息应反映现场的实际情况，所载信息的真实性与检测结论密切相关，否则结论无效！"
msgstr "Basic information is closely related to inspection result, otherwise the conclusion is invalid !"

#: ../../BeltReport.cpp:1037 ../../BeltTearReport.cpp:364
msgid "输送带名称"
msgstr "Belt Name"

#: ../../BeltReport.cpp:1044 ../../BeltTearReport.cpp:371
msgid "输送带制造商"
msgstr "Manufacturer"

#: ../../BeltReport.cpp:1050
msgid "检测长度"
msgstr "Detection length"

#: ../../BeltReport.cpp:1057 ../../BeltTearReport.cpp:384
msgid "输送带用途"
msgstr "Belt purpose"

#: ../../BeltReport.cpp:1063 ../../BeltTearReport.cpp:390
msgid "输送带宽度"
msgstr "Belt width"

#: ../../BeltReport.cpp:1070 ../../BeltTearReport.cpp:397
msgid "输送带规格"
msgstr "Specification"

#: ../../BeltReport.cpp:1076 ../../BeltTearReport.cpp:403
msgid "钢芯直径"
msgstr "Steel core DM"

#: ../../BeltReport.cpp:1083 ../../BeltTearReport.cpp:410
msgid "安装/更换日期"
msgstr "Installation date"

#: ../../BeltReport.cpp:1089 ../../BeltTearReport.cpp:416
msgid "钢芯间距"
msgstr "Core spacing"

#: ../../BeltReport.cpp:1096 ../../BeltTearReport.cpp:423
msgid "输送带拉伸强度"
msgstr "Tensile strength"

#: ../../BeltReport.cpp:1102 ../../BeltTearReport.cpp:429
msgid "钢芯数量"
msgstr "Number of core"

#: ../../BeltReport.cpp:1105 ../../BeltTearReport.cpp:432
msgid " 根"
msgstr ""

#: ../../BeltReport.cpp:1109 ../../BeltTearReport.cpp:436
msgid "覆盖层厚度"
msgstr "Overburden Thickness"

#: ../../BeltReport.cpp:1113 ../../BeltTearReport.cpp:440
msgid "上"
msgstr "Above"

#: ../../BeltReport.cpp:1118
msgid "接头数"
msgstr "Number of joints"

#: ../../BeltReport.cpp:1125 ../../BeltTearReport.cpp:452
msgid "下"
msgstr "Below"

#: ../../BeltReport.cpp:1130
msgid "损伤上限"
msgstr "Scrap upper limit"

#: ../../BeltReport.cpp:1133
msgid " 芯"
msgstr "Core"

#: ../../BeltReport.cpp:1137
msgid "依据标准"
msgstr "Standards"

#: ../../BeltReport.cpp:1140
msgid "GBT 9770 - 2013"
msgstr "GBT 9770 - 2013"

#: ../../BeltReport.cpp:1146 ../../BeltTearReport.cpp:469
msgid "基本信息"
msgstr "Basic information"

#: ../../BeltReport.cpp:1162
msgid "参考结论"
msgstr "Conclusion"

#: ../../BeltReport.cpp:1167
msgid "损 伤 分 级 统 计"
msgstr "Flaw hierarchical statistics"

#: ../../BeltReport.cpp:1171
msgid "五 处 最 大 损 伤"
msgstr "Five maximum flaws"

#: ../../BeltReport.cpp:1187
msgid "断芯数"
msgstr "Broken"

#: ../../BeltReport.cpp:1190 ../../BeltQueryTear.cpp:63
msgid "等级"
msgstr "Level"

#: ../../BeltReport.cpp:1232 ../../BeltQueryFlaw.cpp:345
msgid "疑似干扰"
msgstr "Suspected interference"

#: ../../BeltReport.cpp:1298 ../../BeltChart.cpp:238
msgid "(处)"
msgstr "(Count)"

#: ../../BeltReport.cpp:1305
msgid "五 处 最 大 接 头 抽 动"
msgstr "Five maximum joint shifting"

#: ../../BeltReport.cpp:1311 ../../BeltReport.cpp:1582
#: ../../BeltQueryJoint.cpp:75
msgid "接头号"
msgstr "Joint No."

#: ../../BeltReport.cpp:1314 ../../BeltReport.cpp:1689
#: ../../BeltQueryJoint.cpp:76
msgid "阶梯数"
msgstr "Ladder"

#: ../../BeltReport.cpp:1323
msgid "接头长度"
msgstr "Joint Length"

#: ../../BeltReport.cpp:1441
msgid "检测结果"
msgstr "Detection Result"

#: ../../BeltReport.cpp:1447
msgid "非接头区"
msgstr "Non joint area"

#: ../../BeltReport.cpp:1453
msgid "接头区"
msgstr "Joint area"

#: ../../BeltReport.cpp:1459
msgid "说明"
msgstr "Notes"

#: ../../BeltReport.cpp:1507
msgid "损伤程度分为六个等级："
msgstr "There are six levels of flaw: "

#: ../../BeltReport.cpp:1509
#, c-format
msgid "超限损伤：钢芯输送带局部集中断芯数量达到 %d 芯 以上；"
msgstr "Over limit: The number of locally concentrated broken cores reaches more than %d cores"

#: ../../BeltReport.cpp:1511
#, c-format
msgid "严重损伤：钢芯输送带局部集中断芯数量达到 %.0f~%d 芯；"
msgstr "Severe: The number of locally concentrated broken cores reaches %.0f to %d cores"

#: ../../BeltReport.cpp:1513
#, c-format
msgid "较重损伤：钢芯输送带局部集中断芯数量达到 %.0f~%.0f 芯；"
msgstr "Serious: The number of locally concentrated broken cores reaches %.0f to %.0f cores"

#: ../../BeltReport.cpp:1515
#, c-format
msgid "中度损伤：钢芯输送带局部集中断芯数量达到 %.0f~%.0f 芯；"
msgstr "Medium: The number of locally concentrated broken cores reaches %.0f to %.0f cores"

#: ../../BeltReport.cpp:1517
#, c-format
msgid "轻度损伤：钢芯输送带局部集中断芯数量达到 %.0f~%.0f 芯；"
msgstr "Minor: The number of locally concentrated broken cores reaches %.0f to %.0f cores"

#: ../../BeltReport.cpp:1519
#, c-format
msgid "轻微损伤：钢芯输送带局部集中断芯数量少于 %.0f 芯。"
msgstr "Slight: The number of locally concentrated broken cores is less than %.0f cores"

#: ../../BeltReport.cpp:1521
msgid "关于接头抽动量："
msgstr "About the joint shift: "

#: ../../BeltReport.cpp:1523
msgid "1. 抽动量数值，负值表示为接头的位移量，正值一般是弹性抽动"
msgstr "1. Joint shifting value, negative value is the displacement of the joint, and positive value is generally elastic twitch"

#: ../../BeltReport.cpp:1525
msgid "   或皮带运行不稳定及编码器磨损造成行程误差所导致；"
msgstr "   or travel error caused by unstable belt operation and encoder wear;"

#: ../../BeltReport.cpp:1527
msgid "2. 抽动量数值在 -10 ~ +10 范围内，非多通道连续且无增长趋势"
msgstr "2. The shift value is in the range of - 10~+10, and it is not multi-channel continuous and has no growth trend"

#: ../../BeltReport.cpp:1529
msgid "   具有反复正负跳变现象的，一般为弹性抽动的表现；"
msgstr "   in case of repeated positive and negative jump, it is generally the performance of elastic twitch;"

#: ../../BeltReport.cpp:1531
msgid "3. 偶发的单通道大位移量，非多通道连续且无增长趋势，一般为"
msgstr "3. Sporadic single channel large displacement, non multi-channel continuous and no growth trend, it is generally"

#: ../../BeltReport.cpp:1533
msgid "   皮带抖动、摆动、跳动等，或增补接头后未重做样本所导致。"
msgstr "   caused by belt shaking, swinging, jumping, etc., or failure to redo samples after adding joints."

#: ../../BeltReport.cpp:1548 ../../BeltReport.cpp:1667
#: ../../BeltReport.cpp:1844
msgid "MRT 检 测 报 告"
msgstr "MRT Inspection Report"

#: ../../BeltReport.cpp:1560
msgid "损 伤 序 列 表"
msgstr "Flaw List"

#: ../../BeltReport.cpp:1568
msgid "下表为被检测的钢芯输送带上非接头区域的损伤位置及参考断芯数等信息"
msgstr "The following table shows the damage location and reference number of broken cores in the non joint area of the tested steel core conveyor belt"

#: ../../BeltReport.cpp:1574
msgid "序号"
msgstr "No."

#: ../../BeltReport.cpp:1577
msgid "损伤位置（m）"
msgstr "Flaw Position (m)"

#: ../../BeltReport.cpp:1585
msgid "参照位置"
msgstr "Reference position"

#: ../../BeltReport.cpp:1590
msgid "横向位置（mm）"
msgstr "Lateral position (mm)"

#: ../../BeltReport.cpp:1596
msgid "损伤分级标记"
msgstr "Flaw grade mark"

#: ../../BeltReport.cpp:1679
msgid "接 头 信 息 表"
msgstr "Joint Information"

#: ../../BeltReport.cpp:1692
msgid "接头区域位置（m）"
msgstr "Location of joint area (m)"

#: ../../BeltReport.cpp:1695
msgid "接头长度（mm）"
msgstr "Joint length (mm)"

#: ../../BeltReport.cpp:1698
msgid "样本日期"
msgstr "Sample date"

#: ../../BeltReport.cpp:1721
msgid "接 头 各 通 道 抽 动 量（mm）"
msgstr "Joint shifting of each channel (mm)"

#: ../../BeltReport.cpp:1776
msgid "抽动趋势图"
msgstr "Shifting trend chart"

#: ../../BeltReport.cpp:1785
msgid "Twitch Trend Chart"
msgstr "Shifting trend chart"

#: ../../BeltReport.cpp:1856
msgid "当 前 状 态 弱 磁 检 测 图"
msgstr "Weak magnetic detection diagram of rope current status"

#: ../../BeltReport.cpp:1905 ../../BeltTearReport.cpp:566
msgid "报告打印预览"
msgstr "Report Print Preview"

#: ../../BeltQueryJoint.cpp:64
msgid "接头信息"
msgstr "Joint Info"

#: ../../BeltQueryJoint.cpp:77
msgid "起始位置"
msgstr "Start position"

#: ../../BeltQueryJoint.cpp:78
msgid "结束位置"
msgstr "End position"

#: ../../BeltQueryJoint.cpp:79
msgid "长度"
msgstr "Length"

#: ../../BeltQueryJoint.cpp:80
msgid "样本日"
msgstr "Sample date"

#: ../../BeltQueryJoint.cpp:91
msgid "日期"
msgstr "Date"

#: ../../BeltQueryJoint.cpp:130
msgid "提示： 接头在人工修补后，要对其“重做样本”，右键双击“样本日”按提示操作！"
msgstr "Prompt: After the joint is manually repaired, it is necessary to 'redo the sample' !"

#: ../../BeltQueryJoint.cpp:134
msgid "弹性波动及抽动趋势图"
msgstr "Elastic fluctuation and shift trend chart"

#: ../../BeltQueryJoint.cpp:148
msgid "时间轴：  今天 --------->"
msgstr "Timeline:   Today --------->"

#: ../../BeltQueryJoint.cpp:152
msgid "接头波形图"
msgstr "Joint Waveform"

#: ../../BeltQueryJoint.cpp:495
#, c-format
msgid "趋势图数据获取失败！[%d]"
msgstr "Failed to obtain trend chart data ! [%d]"

#: ../../BeltQueryJoint.cpp:672
msgid "确认重做 接头["
msgstr "Confirm to redo joint["

#: ../../BeltQueryJoint.cpp:672
msgid ""
"] 的样本？\n样本日："
msgstr "] sample ? \nSample date :"

#: ../../BeltQueryJoint.cpp:672
msgid " => "
msgstr " => "

#: ../../BeltQueryJoint.cpp:672 ../../BeltQueryFlaw.cpp:581
#: ../../BeltQueryFlaw.cpp:587
msgid "确认"
msgstr "Confirm"

#: ../../BeltQueryJoint.cpp:687
msgid "重做样本提交成功！"
msgstr "Submit the redo sample successfully !"

msgid "\n样本会在指定样本日之后运行达到样本所需的圈数时自动重做。\n样本重做完成后会更新样本日期，请继续运行一段时间后查看样本日是否已刷新。"
msgstr "\nThe sample will automatically redo when it reaches the required number of cycles after the specified sample day.\nAfter the sample redo is completed, the sample date will be updated.\nPlease continue running for a period of time and check if the sample day has been refreshed."

msgid "二级站连接已断开，或者操作太快了！"
msgstr "The connection to the station has been disconnected, or the operation is too fast !"

#: ../../BeltQueryJoint.cpp:687 ../../BeltQueryJoint.cpp:691
msgid "\n重做样本需要时间，请等待一段时间后重新进入查看样本日是否已刷新。"
msgstr "\nIt takes time to redo the sample. Please wait for a while before entering again to check whether the sample date has been refreshed."

#: ../../BeltQueryJoint.cpp:691
msgid "样本已在重做中，请勿重复操作！"
msgstr "The sample is being redone, please do not repeat the operation !"

#: ../../BeltQueryJoint.cpp:695
msgid "指定的样本重做日期无有效检测数据！\n请在接头修补之后运行一段时间，产生有效数据之后再进行样本重做！"
msgstr "The specified sample redo date has no valid data !\nPlease run for a period of time after the joint repair, and then redo the sample after generating valid data !"

msgid "指定的样本重做日期检测数据不足（未达到样本制作所需圈数）！\n请指定当前日期进行样本重做，提交成功后继续运行。\n运行达到满足样本所需圈数后，会自动重做样本，并更新样本日期。"
msgstr "The specified sample redo date detection data is insufficient.\n(not reaching the required number of circles for sample production)! \nPlease specify the current date for sample redo, and continue running after successful submission.\nAfter running n to meet the required number of cycles for the sample, the sample will be automatically redone and the sample date will be updated."

#: ../../BeltQueryJoint.cpp:699
#, c-format
msgid "重做样本提交失败！\n原因码：%d"
msgstr "Failed to submit the redo sample !\nReason Code : %d"

#: ../../BeltQueryFlaw.cpp:65
msgid "非接头区损伤数据"
msgstr "Non joint area flaw data"

#: ../../BeltQueryFlaw.cpp:77
msgid "绝对位置"
msgstr "Absolute position"

#: ../../BeltQueryFlaw.cpp:78
msgid "相对位置"
msgstr "Relative position"

#: ../../BeltQueryFlaw.cpp:81
msgid "程度"
msgstr "Grade"

#: ../../BeltQueryFlaw.cpp:82
msgid "统计率"
msgstr "Stat rate"

#: ../../BeltQueryFlaw.cpp:83
msgid "报警触发"
msgstr "Alarm trigger"

#: ../../BeltQueryFlaw.cpp:84
msgid "标记已知"
msgstr "Mark known"

#: ../../BeltQueryFlaw.cpp:87
msgid "检测波形图"
msgstr "Detection Waveform"

#: ../../BeltQueryFlaw.cpp:89
msgid "损伤分级统计图"
msgstr "Flaw hierarchical statistics"

#: ../../BeltQueryFlaw.cpp:91
msgid "日期：2022-08-08"
msgstr "Date: 2022-08-08"

#: ../../BeltQueryFlaw.cpp:93
msgid "检测次数：30圈"
msgstr "Number of detect: 30 cycles"

#: ../../BeltQueryFlaw.cpp:95
msgid "平均圈长：1230m"
msgstr "Average lap length: 1230m"

#: ../../BeltQueryFlaw.cpp:97
msgid "过滤："
msgstr "Filter:"

#: ../../BeltQueryFlaw.cpp:216
msgid "日期："
msgstr "Date:"

#: ../../BeltQueryFlaw.cpp:217
#, c-format
msgid "检测次数：%d圈"
msgstr "Number of detect: %d cycles"

#: ../../BeltQueryFlaw.cpp:218
#, c-format
msgid "平均圈长：%.0fm"
msgstr "Average lap length: %.0fm"

#: ../../BeltQueryFlaw.cpp:360 ../../BeltQueryFlaw.cpp:579
#: ../../BeltQueryFlaw.cpp:601 ../../BeltQueryFlaw.cpp:611
#: ../../BeltQueryFlaw.cpp:620
msgid "已知"
msgstr "Known"

#: ../../BeltQueryFlaw.cpp:365 ../../BeltQueryFlaw.cpp:603
msgid "未知"
msgstr "Unknown"

#: ../../BeltQueryFlaw.cpp:479
msgid "\n此日 ["
msgstr "\nThis day ["

#: ../../BeltQueryFlaw.cpp:479
msgid "] 无检测数据！"
msgstr "] no detect data !"

#: ../../BeltQueryFlaw.cpp:581
msgid "确认取消【已知】标记？"
msgstr "Are you sure to cancel the [Known] mark ?"

#: ../../BeltQueryFlaw.cpp:587
msgid "确认标记为【已知】？"
msgstr "Are you sure to mark it as [Known] ?"

#: ../../BeltQueryFlaw.cpp:607
msgid "取消标记成功！"
msgstr "Unmarking succeeded !"

#: ../../BeltQueryFlaw.cpp:615
msgid "标记成功！"
msgstr "Marking succeeded !"

#: ../../BeltQueryFlaw.cpp:621
#, c-format
msgid "取消标记失败！\n原因码：%d"
msgstr "Unmark failed !\nReason Code: %d"

#: ../../BeltQueryFlaw.cpp:623
#, c-format
msgid "标记失败！\n原因码：%d"
msgstr "Marking failed !\nReason Code: %d"

#: ../../BeltQueryTear.cpp:46
msgid "纵向撕裂"
msgstr "Longitudinal Tear"

#: ../../BeltQueryTear.cpp:59
msgid "纵撕时间"
msgstr "Tear Time"

#: ../../BeltQueryTear.cpp:60
msgid "纵撕位置"
msgstr "Tear Position"

#: ../../BeltQueryTear.cpp:61
msgid "纵撕长度"
msgstr "Tear Length"

#: ../../BeltQueryTear.cpp:62
msgid "纵撕宽度"
msgstr "Tear Width"

#: ../../BeltQueryTear.cpp:67
msgid "纵撕照片："
msgstr "Tear Photo:"

#: ../../BeltQueryTear.cpp:68
msgid "打印报告"
msgstr "Print Report"

#: ../../BeltQueryTear.cpp:145 ../../BeltQueryTear.cpp:305
#: ../../BeltQueryTear.cpp:322 ../../BeltQueryTear.cpp:342
#: ../../BeltTearReport.cpp:461
msgid "停机"
msgstr "Shutdown"

#: ../../BeltQueryTear.cpp:212
#, c-format
msgid "照片获取失败！[%d]"
msgstr "Photo acquisition failed ! [%d]"

#: ../../BeltQueryTear.cpp:241
#, c-format
msgid "未知图片格式！[%d]\n"
msgstr "Unknown picture format ! [%d]\n"

#: ../../BeltQueryTear.cpp:287
msgid "无纵撕记录！"
msgstr "No tear record !"

#: ../../AccountDialog.cpp:45
msgid "账户管理"
msgstr "Account management"

#: ../../AccountDialog.cpp:50
msgid "账户列表："
msgstr "Account list:"

#: ../../AccountDialog.cpp:61
msgid "账户名"
msgstr "Account name"

#: ../../AccountDialog.cpp:62
msgid "密码"
msgstr "Password"

#: ../../AccountDialog.cpp:63 ../../AccountDialog.cpp:84
msgid "有效期"
msgstr "Valid Date"

#: ../../AccountDialog.cpp:64
msgid "可访问站"
msgstr "Accessible station"

#: ../../AccountDialog.cpp:67
msgid "检测站列表："
msgstr "Station list:"

#: ../../AccountDialog.cpp:78
msgid "序列号"
msgstr "Serial num"

#: ../../AccountDialog.cpp:79
msgid "节点号"
msgstr "Node num"

#: ../../AccountDialog.cpp:80
msgid "名称"
msgstr "Name"

#: ../../AccountDialog.cpp:81
msgid "站类型"
msgstr "Type"

#: ../../AccountDialog.cpp:82
msgid "IP地址"
msgstr "IP Address"

#: ../../AccountDialog.cpp:83
msgid "版本号"
msgstr "Version"

#: ../../AccountDialog.cpp:87
msgid "序列号："
msgstr "Serial num:"

#: ../../AccountDialog.cpp:88 ../../AccountDialog.cpp:99
msgid "新增"
msgstr "Add"

#: ../../AccountDialog.cpp:90
msgid "节点号："
msgstr "Node num:"

#: ../../AccountDialog.cpp:92 ../../AccountDialog.cpp:100
msgid "删除"
msgstr "Delete"

#: ../../AccountDialog.cpp:93
msgid "用户名："
msgstr "User Name:"

#: ../../AccountDialog.cpp:95
msgid "密码："
msgstr "Password:"

#: ../../AccountDialog.cpp:97
msgid "有效期："
msgstr "Valid date:"

#: ../../AccountDialog.cpp:101
msgid "名称："
msgstr "Name:"

#: ../../AccountDialog.cpp:166 ../../AccountDialog.cpp:219
msgid "登陆超时，请重新登陆！"
msgstr "Login timeout, please login again !"

#: ../../AccountDialog.cpp:168 ../../AccountDialog.cpp:221
msgid "权限不足 !"
msgstr "Permission denied !"

#: ../../AccountDialog.cpp:170 ../../AccountDialog.cpp:223
msgid "操作失败！"
msgstr "Operation failed !"

#: ../../AccountDialog.cpp:258
msgid "请输入账户名！"
msgstr "Please enter the account name !"

#: ../../AccountDialog.cpp:264
msgid "请输入密码！"
msgstr "Please enter the password !"

#: ../../AccountDialog.cpp:270
msgid "请选择可访问站点！"
msgstr "Please select the accessible station !"

#: ../../AccountDialog.cpp:295
msgid "请选择要删除的账户！"
msgstr "Please select the account to delete !"

#: ../../AccountDialog.cpp:301
msgid "管理员账户不可删除！"
msgstr "The administrator account cannot be deleted !"

#: ../../AccountDialog.cpp:321
msgid "请输入序列号！"
msgstr "Please enter the serial number !"

#: ../../AccountDialog.cpp:343
msgid "请输入站点名！"
msgstr "Please enter the station name !"

#: ../../AccountDialog.cpp:365
msgid "请选择要删除的站点！\n注：点击左侧数字序号选择一行后进行删除。"
msgstr "Please select the station to delete !\nNote: click the number on the left to select a line and delete it."

#: ../../AccountDialog.cpp:368
msgid "删除站点将会清空所有数据！\n\n请确认是否真的要删除站点？"
msgstr "Deleting the site will clear all data !\n\nAre you sure you want to delete the station ?"

#: ../../AccountDialog.cpp:389
msgid "请输入密码："
msgstr "Please enter the password:"

#: ../../AccountDialog.cpp:389
msgid "修改密码"
msgstr "Change Password"

#: ../../AccountDialog.cpp:396
msgid "请输入有效期："
msgstr "Please enter the valid date:"

#: ../../AccountDialog.cpp:396
msgid "修改有效期"
msgstr "Change valid date"

#: ../../AccountDialog.cpp:426
msgid "请输入站点名："
msgstr "Please enter the station name:"

#: ../../AccountDialog.cpp:426
msgid "修改名称"
msgstr "Change Name"

#: ../../BeltChart.cpp:121
msgid "(芯)"
msgstr "(Core)"

#: ../../MainFrame.cpp:169
msgid "钢丝绳芯输送带在线实时安全监测系统"
msgstr "Conveyor belt safety monitoring system"

#: ../../MainFrame.cpp:174
msgid "电梯钢丝绳在线实时安全监测系统"
msgstr "Elevator wire rope safety monitoring system"

#: ../../MainFrame.cpp:179
msgid "架空乘人钢丝绳在线实时安全监测系统"
msgstr "Circulating wire rope safety monitoring system"

#: ../../MainFrame.cpp:184
msgid "索道钢丝绳在线实时安全监测系统"
msgstr "Cableway wire rope safety monitoring system"

#: ../../MainFrame.cpp:189
msgid "钢丝绳在线实时安全监测系统"
msgstr "Wire rope safety centralized monitoring system"

#: ../../MainFrame.cpp:1007
msgid "断芯"
msgstr "Flaw"

#: ../../MainFrame.cpp:1007
msgid "接头"
msgstr "Joint"

#: ../../MainFrame.cpp:1007
msgid "纵撕"
msgstr "Tear"

#: ../../MainFrame.cpp:1139 ../../MainFrame.cpp:1275 ../../BeltFrame.cpp:309
#: ../../BeltFrame.cpp:715
msgid "状态：离线"
msgstr "Status: Offline"

#: ../../MainFrame.cpp:1144 ../../MainFrame.cpp:1280 ../../BeltFrame.cpp:313
#: ../../BeltFrame.cpp:719
msgid "状态：停止检测"
msgstr "Status: Stop"

#: ../../MainFrame.cpp:1149 ../../BeltFrame.cpp:319 ../../BeltFrame.cpp:725
#, c-format
msgid "找到接头：%s / %d"
msgstr "Find joint: %s / %d"

msgid "找到接头：%d/%d  [%s]"
msgstr "Find joint: %d/%d  [%s]"

#: ../../MainFrame.cpp:1154 ../../BeltFrame.cpp:323 ../../BeltFrame.cpp:729
msgid "正在寻找特征接头"
msgstr "Finding feature joint"

#: ../../MainFrame.cpp:1163
#, c-format
msgid "带长：%.0fm"
msgstr "Belt length: %.0fm"

#: ../../MainFrame.cpp:1168 ../../MainFrame.cpp:1306
#, c-format
msgid "实时位置：%.0fm"
msgstr "Position: %.0fm"

#: ../../MainFrame.cpp:1173
#, c-format
msgid "带宽：%dmm"
msgstr "Belt width: %dmm"

#: ../../MainFrame.cpp:1178 ../../MainFrame.cpp:1316
#, c-format
msgid "实时速度：%.1fm/s"
msgstr "Speed: %.1fm/s"

#: ../../MainFrame.cpp:1183 ../../MainFrame.cpp:1322
#, c-format
msgid "规格：%s"
msgstr "Spec: %s"

#: ../../MainFrame.cpp:1192
msgid "设备状态"
msgstr "Device state"

#: ../../MainFrame.cpp:1192
msgid "损伤状态"
msgstr "Flaw state"

#: ../../MainFrame.cpp:1285 ../../BeltFrame.cpp:328 ../../BeltFrame.cpp:734
msgid "状态：检测中"
msgstr "Status: Detecting"

#: ../../MainFrame.cpp:1294
#, c-format
msgid "钢丝绳长：%.0fm"
msgstr "Rope length: %.0fm"

#: ../../MainFrame.cpp:1300
#, c-format
msgid "实时位置：%.1fm|%.1fm"
msgstr "Position: %.1fm|%.1fm"

#: ../../MainFrame.cpp:1302
#, c-format
msgid "实时位置：%.1fm"
msgstr "Position: %.1fm"

#: ../../MainFrame.cpp:1304
#, c-format
msgid "实时位置：%.1f°"
msgstr "Position: %.1f°"

#: ../../MainFrame.cpp:1311
#, c-format
msgid "钢丝绳数：%d根"
msgstr "Rope count: %d"

#: ../../MainFrame.cpp:1324
#, c-format
msgid "规格：%d x %d"
msgstr "Spec: %d x %d"

#: ../../BeltFrame.cpp:171
#msgid "钢丝绳芯输送带在线实时安全监测系统"
#msgstr "Conveyor Belt real-time safety monitoring system"

#: ../../BeltFrame.cpp:173
#msgid "钢丝绳在线实时安全监测系统"
#msgstr "Wire rope real-time safety monitoring system"

#: ../../BeltFrame.cpp:195
msgid "五处最大损伤"
msgstr "Five maximum flaws"

#: ../../BeltFrame.cpp:215
msgid "损伤分级统计"
msgstr "Flaw hierarchical statistics"

#: ../../BeltFrame.cpp:227
msgid "五处最大抽动"
msgstr "Five maximum shift"

#: ../../BeltFrame.cpp:247
msgid "纵撕实时图像"
msgstr "Tear real-time image"

#: ../../BeltFrame.cpp:277
msgid "运行动态"
msgstr "Running state"

#: ../../BeltFrame.cpp:282
msgid "视觉实时图像"
msgstr "VI real-time image"

#: ../../BeltFrame.cpp:331 ../../BeltFrame.cpp:737
#, c-format
msgid "位置：%.0fm"
msgstr "Position: %.0fm"

#: ../../BeltFrame.cpp:333 ../../BeltFrame.cpp:739
#, c-format
msgid "速度：%.1fm/s"
msgstr "Speed: %.1fm/s"

#: ../../BeltQueryTearAbn.cpp:28
msgid "纵撕故障记录"
msgstr "Tear failure log"

#: ../../BeltQueryTearAbn.cpp:43
msgid "故障时间"
msgstr "Failure time"

#: ../../BeltQueryTearAbn.cpp:44
msgid "故障点"
msgstr "Failure point"

#: ../../BeltQueryTearAbn.cpp:58
msgid "故障记录："
msgstr "Failure Log:"

#: ../../BeltQueryTearAbn.cpp:59
msgid "水箱缺水报警记录："
msgstr "Water shortage alarm log:"

#: ../../BeltQueryTearAbn.cpp:105
msgid "1号相机掉线"
msgstr "Camera 1 is disconnected"

#: ../../BeltQueryTearAbn.cpp:107
msgid "2号相机掉线"
msgstr "Camera 2 is disconnected"

#: ../../BeltQueryTearAbn.cpp:109
msgid "水箱通信失败"
msgstr "Water tank communication failure"

#: ../../BeltQueryTearAbn.cpp:111
msgid "控制通信失败"
msgstr "Control communication failure"

#: ../../BeltTearReport.cpp:90
msgid "纵撕检测报告"
msgstr "Tear Inspection Report"

#: ../../BeltTearReport.cpp:99
msgid "钢芯输送带纵撕检测报告"
msgstr "Steel Core Conveyor Belt Tear Report"

#: ../../BeltTearReport.cpp:330
msgid "检验： "
msgstr "Inspector: "

#: ../../BeltTearReport.cpp:332
msgid "审核： "
msgstr "Auditor: "

#: ../../BeltTearReport.cpp:347
msgid "纵 撕 检 测 主 报 告"
msgstr "Tear Inspection Report"

#: ../../BeltTearReport.cpp:377
msgid "输送带长度"
msgstr "Belt length"

#: ../../BeltTearReport.cpp:445
msgid "纵撕伤数"
msgstr "Tear count"

#: ../../BeltTearReport.cpp:457
msgid "报告类型"
msgstr "Report type"

#: ../../BeltTearReport.cpp:485
msgid "最 大 撕 裂 伤"
msgstr "Maximum Tear"

#: ../../BeltTearReport.cpp:490 ../../BeltTearReport.cpp:531
#, c-format
msgid "位置：%.2fm    长度：%.2fm    宽度：%dmm    时间："
msgstr "Position: %.2fm    Length: %.2fm    Width: %dmm    Time:"

#: ../../BeltTearReport.cpp:510
msgid "纵 撕 检 测 报 告"
msgstr "Tear Inspection Report"

#: ../../BeltTearReport.cpp:522
msgid "纵 撕 伤 列 表"
msgstr "Tear List"
