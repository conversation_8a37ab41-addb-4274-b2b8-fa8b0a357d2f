<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_layout_file>
	<FileVersion major="1" minor="0" />
	<ActiveTarget name="Release" />
	<File name="BeltFrame.h" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3006" topLine="104" />
		</Cursor>
	</File>
	<File name="ZXQueryAlarm.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="698" topLine="24" />
		</Cursor>
	</File>
	<File name="BeltQueryFlaw.cpp" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="9120" topLine="163" />
		</Cursor>
	</File>
	<File name="tcp_client_web.h" open="0" top="0" tabpos="9" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="7586" topLine="158" />
		</Cursor>
	</File>
	<File name="BeltCurve.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="302" topLine="0" />
		</Cursor>
	</File>
	<File name="ZXQueryFlaw.cpp" open="0" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="11987" topLine="393" />
		</Cursor>
		<Folding>
			<Collapse line="66" />
			<Collapse line="319" />
			<Collapse line="555" />
			<Collapse line="601" />
		</Folding>
	</File>
	<File name="BeltQueryFlaw.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="574" topLine="17" />
		</Cursor>
		<Folding>
			<Collapse line="40" />
			<Collapse line="44" />
		</Folding>
	</File>
	<File name="ZXReport.cpp" open="0" top="0" tabpos="6" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="17765" topLine="177" />
		</Cursor>
		<Folding>
			<Collapse line="115" />
			<Collapse line="215" />
			<Collapse line="568" />
		</Folding>
	</File>
	<File name="BeltJointCurve.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1020" topLine="8" />
		</Cursor>
	</File>
	<File name="RunningView.h" open="0" top="0" tabpos="8" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="904" topLine="18" />
		</Cursor>
	</File>
	<File name="Validity.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="203" topLine="0" />
		</Cursor>
	</File>
	<File name="TCK_W_Client_IoTApp.cpp" open="1" top="0" tabpos="6" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="8152" topLine="311" />
		</Cursor>
	</File>
	<File name="Control\BitmapButton.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="634" topLine="0" />
		</Cursor>
	</File>
	<File name="TCK_W_Client_IoTMain.cpp" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="4000" topLine="154" />
		</Cursor>
	</File>
	<File name="Control\BitmapButton.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="327" topLine="0" />
		</Cursor>
	</File>
	<File name="AccountDialog.h" open="0" top="0" tabpos="8" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2074" topLine="45" />
		</Cursor>
	</File>
	<File name="StatChart.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1673" topLine="50" />
		</Cursor>
	</File>
	<File name="ZXStatDialog.cpp" open="0" top="0" tabpos="2" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2264" topLine="36" />
		</Cursor>
	</File>
	<File name="StatChart.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="201" topLine="0" />
		</Cursor>
	</File>
	<File name="Control\TransStaticText.cpp" open="0" top="0" tabpos="7" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1033" topLine="49" />
		</Cursor>
		<Folding>
			<Collapse line="41" />
		</Folding>
	</File>
	<File name="ZXQueryAlarm.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3256" topLine="64" />
		</Cursor>
		<Folding>
			<Collapse line="112" />
		</Folding>
	</File>
	<File name="TCK_W_Client_IoTApp.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="595" topLine="5" />
		</Cursor>
	</File>
	<File name="MainFrame.cpp" open="1" top="0" tabpos="9" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="20086" topLine="552" />
		</Cursor>
	</File>
	<File name="tcp_client_view.cpp" open="0" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="7399" topLine="275" />
		</Cursor>
	</File>
	<File name="RunningView.cpp" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="14441" topLine="406" />
		</Cursor>
	</File>
	<File name="TCK_W_Client_IoTMain.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="320" topLine="5" />
		</Cursor>
	</File>
	<File name="BeltReport.cpp" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="97627" topLine="1176" />
		</Cursor>
		<Folding>
			<Collapse line="307" />
			<Collapse line="330" />
			<Collapse line="401" />
			<Collapse line="434" />
			<Collapse line="488" />
			<Collapse line="493" />
			<Collapse line="507" />
			<Collapse line="516" />
			<Collapse line="522" />
			<Collapse line="534" />
			<Collapse line="549" />
			<Collapse line="556" />
			<Collapse line="618" />
			<Collapse line="623" />
			<Collapse line="633" />
			<Collapse line="659" />
			<Collapse line="667" />
			<Collapse line="719" />
			<Collapse line="734" />
			<Collapse line="744" />
			<Collapse line="793" />
			<Collapse line="840" />
			<Collapse line="872" />
			<Collapse line="907" />
			<Collapse line="1048" />
			<Collapse line="1069" />
			<Collapse line="1138" />
			<Collapse line="1144" />
			<Collapse line="1265" />
			<Collapse line="1274" />
			<Collapse line="1331" />
			<Collapse line="1338" />
			<Collapse line="1397" />
			<Collapse line="1482" />
			<Collapse line="1520" />
			<Collapse line="1595" />
			<Collapse line="1623" />
			<Collapse line="1652" />
			<Collapse line="1688" />
			<Collapse line="1752" />
			<Collapse line="1773" />
			<Collapse line="2104" />
			<Collapse line="2115" />
			<Collapse line="2184" />
			<Collapse line="2241" />
		</Folding>
	</File>
	<File name="BeltTearReport.cpp" open="0" top="0" tabpos="5" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="14220" topLine="192" />
		</Cursor>
		<Folding>
			<Collapse line="185" />
			<Collapse line="232" />
		</Folding>
	</File>
	<File name="tcp_client_srcdata.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2160" topLine="49" />
		</Cursor>
	</File>
	<File name="BeltReport.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="89" topLine="0" />
		</Cursor>
		<Folding>
			<Collapse line="36" />
		</Folding>
	</File>
	<File name="BeltQueryTearAbn.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="178" topLine="0" />
		</Cursor>
	</File>
	<File name="CameraManager.cpp" open="1" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3914" topLine="132" />
		</Cursor>
	</File>
	<File name="ZXQueryFlaw.h" open="1" top="0" tabpos="7" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2531" topLine="94" />
		</Cursor>
	</File>
	<File name="Control\TransStaticText.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="566" topLine="1" />
		</Cursor>
	</File>
	<File name="CircleManager.cpp" open="1" top="0" tabpos="5" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2023" topLine="62" />
		</Cursor>
	</File>
	<File name="Validity.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="15828" topLine="130" />
		</Cursor>
		<Folding>
			<Collapse line="64" />
			<Collapse line="141" />
		</Folding>
	</File>
	<File name="DebugWin.cpp" open="0" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="993" topLine="10" />
		</Cursor>
	</File>
	<File name="DebugWin.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="202" topLine="0" />
		</Cursor>
	</File>
	<File name="AccountDialog.cpp" open="0" top="0" tabpos="7" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="7440" topLine="128" />
		</Cursor>
	</File>
	<File name="Control\TransStaticBmp.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="217" topLine="2" />
		</Cursor>
	</File>
	<File name="analogChart.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="350" topLine="17" />
		</Cursor>
	</File>
	<File name="BeltChart.cpp" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="918" topLine="6" />
		</Cursor>
	</File>
	<File name="BeltQueryTearAbn.cpp" open="0" top="0" tabpos="3" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3245" topLine="48" />
		</Cursor>
	</File>
	<File name="analogChart.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="9549" topLine="227" />
		</Cursor>
		<Folding>
			<Collapse line="159" />
			<Collapse line="261" />
			<Collapse line="275" />
			<Collapse line="304" />
			<Collapse line="361" />
			<Collapse line="369" />
			<Collapse line="377" />
		</Folding>
	</File>
	<File name="BeltTearReport.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="481" topLine="17" />
		</Cursor>
		<Folding>
			<Collapse line="41" />
			<Collapse line="89" />
		</Folding>
	</File>
	<File name="BeltQueryAlarm.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="5278" topLine="0" />
		</Cursor>
	</File>
	<File name="tcp_client_srcdata.cpp" open="0" top="0" tabpos="7" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="744" topLine="10" />
		</Cursor>
		<Folding>
			<Collapse line="228" />
			<Collapse line="264" />
			<Collapse line="494" />
		</Folding>
	</File>
	<File name="BeltYBRedoSelDate.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1745" topLine="29" />
		</Cursor>
	</File>
	<File name="BeltQueryJoint.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="486" topLine="12" />
		</Cursor>
		<Folding>
			<Collapse line="38" />
			<Collapse line="69" />
			<Collapse line="86" />
		</Folding>
	</File>
	<File name="PhotoView.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="160" topLine="0" />
		</Cursor>
	</File>
	<File name="BeltChart.h" open="0" top="0" tabpos="6" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1310" topLine="0" />
		</Cursor>
	</File>
	<File name="CaptureConfig.cpp" open="1" top="0" tabpos="2" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1225" topLine="32" />
		</Cursor>
	</File>
	<File name="BeltQueryJoint.cpp" open="1" top="0" tabpos="3" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="28440" topLine="853" />
		</Cursor>
	</File>
	<File name="PhotoViewer.cpp" open="1" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="7050" topLine="214" />
		</Cursor>
	</File>
	<File name="BeltCurve.cpp" open="0" top="0" tabpos="2" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1155" topLine="267" />
		</Cursor>
	</File>
	<File name="BeltQueryAlarm.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="199" topLine="28" />
		</Cursor>
	</File>
	<File name="NoteWin.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="22471" topLine="27" />
		</Cursor>
	</File>
	<File name="BeltFrame.cpp" open="1" top="0" tabpos="8" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="50720" topLine="1576" />
		</Cursor>
	</File>
	<File name="ZXReport.h" open="0" top="0" tabpos="5" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="5410" topLine="48" />
		</Cursor>
		<Folding>
			<Collapse line="36" />
			<Collapse line="103" />
		</Folding>
	</File>
	<File name="BeltQueryTear.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1329" topLine="38" />
		</Cursor>
	</File>
	<File name="BeltQueryTear.cpp" open="0" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="724" topLine="31" />
		</Cursor>
	</File>
	<File name="PhotoView.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="275" topLine="0" />
		</Cursor>
	</File>
	<File name="tcp_client_web.cpp" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="18033" topLine="625" />
		</Cursor>
	</File>
	<File name="ZXStatDialog.h" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="335" topLine="0" />
		</Cursor>
	</File>
	<File name="Control\TransStaticBmp.cpp" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="2498" topLine="132" />
		</Cursor>
	</File>
	<File name="BeltJointCurve.h" open="0" top="0" tabpos="0" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="274" topLine="4" />
		</Cursor>
	</File>
	<File name="MainFrame.h" open="0" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3002" topLine="81" />
		</Cursor>
	</File>
	<File name="tcp_client_view.h" open="0" top="0" tabpos="5" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="559" topLine="12" />
		</Cursor>
	</File>
</CodeBlocks_layout_file>
