#ifndef BELTREPORT_H
#define BELTREPORT_H

//(*Headers(BeltReport)
#include <wx/button.h>
#include <wx/checkbox.h>
#include <wx/choice.h>
#include <wx/dialog.h>
#include <wx/gauge.h>
#include <wx/statbox.h>
#include <wx/stattext.h>
#include <wx/textctrl.h>
#include <wx/treectrl.h>
//*)
#include <wx/print.h>
#include "tcp_client_web.h"
#include <string>
#include "BeltQueryFlaw.h"
#include "BeltQueryJoint.h"
#include "tcp_client_srcdata.h"

class BeltReport: public wxDialog, public ThreadBase
{
	public:

		BeltReport(wxWindow* parent, const char *sn, const char *node, const char *name, const TcpClientWeb::DataEvtReplyRope *rope_info, int sscnt, int uplimit,
					wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltReport();

		void SetData(Redis& jdata);
		void SetJtData(Redis& jdata);
		void SetJtYB(Redis& jdata);
		void SetJtDaily(Redis& jdata);

		void ThreadFuncReport(void *arg, unsigned int len);

		//(*Declarations(BeltReport)
		wxButton* Button1;
		wxCheckBox* CheckBox1;
		wxCheckBox* CheckBox2;
		wxCheckBox* CheckBox3;
		wxCheckBox* CheckBox4;
		wxChoice* Choice1;
		wxChoice* Choice2;
		wxGauge* Gauge1;
		wxStaticBox* StaticBox1;
		wxStaticBox* StaticBox2;
		wxStaticBox* StaticBox3;
		wxStaticBox* StaticBox4;
		wxStaticText* StaticText10;
		wxStaticText* StaticText11;
		wxStaticText* StaticText12;
		wxStaticText* StaticText13;
		wxStaticText* StaticText14;
		wxStaticText* StaticText15;
		wxStaticText* StaticText16;
		wxStaticText* StaticText17;
		wxStaticText* StaticText18;
		wxStaticText* StaticText19;
		wxStaticText* StaticText1;
		wxStaticText* StaticText20;
		wxStaticText* StaticText21;
		wxStaticText* StaticText22;
		wxStaticText* StaticText23;
		wxStaticText* StaticText24;
		wxStaticText* StaticText25;
		wxStaticText* StaticText26;
		wxStaticText* StaticText27;
		wxStaticText* StaticText2;
		wxStaticText* StaticText3;
		wxStaticText* StaticText4;
		wxStaticText* StaticText5;
		wxStaticText* StaticText6;
		wxStaticText* StaticText7;
		wxStaticText* StaticText8;
		wxStaticText* StaticText9;
		wxTextCtrl* TextCtrl10;
		wxTextCtrl* TextCtrl11;
		wxTextCtrl* TextCtrl12;
		wxTextCtrl* TextCtrl13;
		wxTextCtrl* TextCtrl14;
		wxTextCtrl* TextCtrl15;
		wxTextCtrl* TextCtrl16;
		wxTextCtrl* TextCtrl17;
		wxTextCtrl* TextCtrl18;
		wxTextCtrl* TextCtrl1;
		wxTextCtrl* TextCtrl2;
		wxTextCtrl* TextCtrl3;
		wxTextCtrl* TextCtrl4;
		wxTextCtrl* TextCtrl5;
		wxTextCtrl* TextCtrl6;
		wxTextCtrl* TextCtrl7;
		wxTextCtrl* TextCtrl8;
		wxTextCtrl* TextCtrl9;
		wxTreeCtrl* TreeCtrl1;
		//*)

	protected:

		void OnJointTrendProgress(wxCommandEvent& event);
		void OnSrcDataProgress(wxCommandEvent& event);
		void OnReportOK(wxCommandEvent& event);

		//(*Identifiers(BeltReport)
		static const long ID_STATICBOX2;
		static const long ID_STATICBOX1;
		static const long ID_STATICTEXT1;
		static const long ID_TEXTCTRL1;
		static const long ID_STATICTEXT2;
		static const long ID_TEXTCTRL2;
		static const long ID_STATICTEXT3;
		static const long ID_TEXTCTRL3;
		static const long ID_STATICTEXT4;
		static const long ID_TEXTCTRL4;
		static const long ID_STATICTEXT5;
		static const long ID_TEXTCTRL5;
		static const long ID_TREECTRL1;
		static const long ID_STATICTEXT6;
		static const long ID_STATICTEXT7;
		static const long ID_TEXTCTRL6;
		static const long ID_STATICTEXT8;
		static const long ID_TEXTCTRL7;
		static const long ID_STATICTEXT9;
		static const long ID_TEXTCTRL8;
		static const long ID_STATICTEXT10;
		static const long ID_STATICTEXT11;
		static const long ID_TEXTCTRL9;
		static const long ID_STATICTEXT12;
		static const long ID_STATICTEXT13;
		static const long ID_TEXTCTRL10;
		static const long ID_STATICTEXT14;
		static const long ID_STATICTEXT15;
		static const long ID_TEXTCTRL11;
		static const long ID_STATICTEXT16;
		static const long ID_TEXTCTRL12;
		static const long ID_STATICTEXT17;
		static const long ID_TEXTCTRL13;
		static const long ID_STATICTEXT18;
		static const long ID_TEXTCTRL14;
		static const long ID_TEXTCTRL15;
		static const long ID_STATICBOX3;
		static const long ID_CHECKBOX1;
		static const long ID_CHECKBOX2;
		static const long ID_CHECKBOX3;
		static const long ID_STATICBOX4;
		static const long ID_CHOICE1;
		static const long ID_STATICTEXT19;
		static const long ID_CHECKBOX4;
		static const long ID_STATICTEXT20;
		static const long ID_CHOICE2;
		static const long ID_STATICTEXT21;
		static const long ID_TEXTCTRL16;
		static const long ID_STATICTEXT22;
		static const long ID_GAUGE1;
		static const long ID_BUTTON1;
		static const long ID_STATICTEXT23;
		static const long ID_TEXTCTRL17;
		static const long ID_STATICTEXT24;
		static const long ID_TEXTCTRL18;
		static const long ID_STATICTEXT25;
		static const long ID_STATICTEXT26;
		static const long ID_STATICTEXT27;
		//*)

	private:

		wxWindow *m_parent;
		wxTreeItemId m_treeRoot;
		std::string m_sn;
		std::string m_node;
		TcpClientWeb::DataEvtReplyRope m_rope_info;
		int m_sscnt;
		int m_uplimit;

		float m_detect_len;
		std::list<FlawData> m_flaw_data;
		FlawData m_flaw_max5[5];
		int m_levCnt[6];
		std::list<JointData> m_joint_data;
		JointData *m_joint_max5[5];
		int m_jtdt_cnt;

		TcpClientSrcdata *m_tcp_srcdata;

		typedef struct
		{
			wxString fname;
			long points;
			float sstep;
		} SrcDataInfo;
		std::map<wxString, SrcDataInfo> m_obtained_files;
		SrcDataInfo m_srcdata_info;

		//(*Handlers(BeltReport)
		void OnTreeCtrl1SelectionChanged(wxTreeEvent& event);
		void OnButton1Click(wxCommandEvent& event);
		//*)

		DECLARE_EVENT_TABLE()

		std::vector<wxBitmap> m_previewBmp;
		bool m_isPrintPreviewExist;
        wxPrintData m_printData;
        wxPrintDialogData m_printDialogData;
        wxPageSetupDialogData m_pageSetupDialogData;
        unsigned int m_totalPages;

	public:
		friend class BeltPrintout;
};


class BeltPrintout : public wxPrintout
{
    public:
        BeltPrintout(BeltReport *parent);
        ~BeltPrintout();
        bool OnPrintPage(int pageNum);
        bool HasPage(int pageNum);
        void GetPageInfo(int *minPage, int *maxPage, int *selPageFrom, int *selPageTo);
    private:
        BeltReport *m_parent;
};

#endif
