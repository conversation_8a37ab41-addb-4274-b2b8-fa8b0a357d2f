#ifndef ZXSTATDIALOG_H
#define ZXSTATDIALOG_H

//(*Headers(ZXStatDialog)
#include <wx/dialog.h>
#include <wx/statbox.h>
//*)
#include "yblib.h"
#include <wx/panel.h>
#include <wx/bitmap.h>
#include <wx/dcmemory.h>
#include <wx/datetime.h>
#include <vector>
#include "StatChart.h"

class ZXStatDialog: public wxDialog
{
	public:

		ZXStatDialog(wxWindow* parent, const char *sn, const char *node, float ropeLen, wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~ZXStatDialog();

		void SetData(Redis& jdata);

		//(*Declarations(ZXStatDialog)
		wxStaticBox* StaticBox1;
		wxStaticBox* StaticBox2;
		wxStaticBox* StaticBox3;
		wxStaticBox* StaticBox4;
		wxStaticBox* StaticBox5;
		//*)

	protected:

		//(*Identifiers(ZXStatDialog)
		static const long ID_STATICBOX1;
		static const long ID_STATICBOX2;
		static const long ID_STATICBOX3;
		static const long ID_STATICBOX4;
		static const long ID_STATICBOX5;
		//*)

	private:

		std::string m_sn;
		std::string m_node;
		float m_ropeLen;
		int m_step;

		//(*Handlers(ZXStatDialog)
		//*)

		DECLARE_EVENT_TABLE()
};

#endif
