#ifndef RUNNINGVIEW_H
#define RUNNINGVIEW_H

#include <wx/panel.h>
#include <wx/bitmap.h>

class RunningView: public wxPanel
{
	public:

		RunningView(wxWindow* parent,wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize, int fontSize=8, bool isBgBmp=true);
		virtual ~RunningView();

		void SetPosition(const wxPoint &  pt);
        void SetSize(const wxSize &  size);

        void SetCntDayTotal(int daily, int total);
        void SetSpreaderPos(int shpos, int tppos);
        void SetTrolleyPos(int tppos);
        void SetGirderPos(int gapos);
        void SetTripPos(int trip, int trip_cnt, float speed);
        void SetPos(float pos, float ropeLen, float speed);
        void SetMileageDayTotal(float daily, float total);

	private:

		wxWindow *m_parent;
		wxBitmap m_bgBitmap;
		wxBitmap m_firstBgBmp;
		wxPoint m_pos;
		wxSize m_srcSize;
		wxSize m_size;
        wxFont m_font;
        int m_fontSize;

        int m_type;

        wxBitmap m_craneBmp;
        wxBitmap m_hangBmp;
        wxSize m_craneSize;
        int m_paddingX;
        int m_paddingY;
        int m_hang_x;
        int m_hang_y;
        double m_beam_radian;
        int m_cnt_daily;
        int m_cnt_total;
        float m_mileage_daily;
        float m_mileage_total;

        wxBitmap m_upBmp;
        wxBitmap m_dnBmp;
        int m_ori;
        int m_trip;
        int m_btrip;
        int m_ttrip;
        float m_cpos;
        float m_spos;
        float m_epos;

        void OnEraseBackground(wxEraseEvent& event);
		void OnPaint(wxPaintEvent& event);

		void SizeChanged();

		DECLARE_EVENT_TABLE()
};

#endif
