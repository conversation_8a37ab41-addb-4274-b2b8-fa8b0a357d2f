#ifndef ANALOG_CHART_H
#define ANALOG_CHART_H

#include <wx/wx.h>
#include <wx/panel.h>
#include <wx/dcmemory.h>
#include <wx/bitmap.h>
#include <vector>

typedef struct
{
	float pos;
	int ss;
	int ee;
	float value;
} AnalogFlawData;

typedef struct
{
	char num[16];
	float spos;
	float epos;
	int lv;
} AnalogJtData;

class AnalogChart : public wxEvtHandler
{
    public:
        AnalogChart(wxWindow* parent,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
        ~AnalogChart();
		///指定字体、背景色、坐标颜色、接头标号颜色
		void SetBG(const wxFont *font, const wxColour *bgColor, const wxColour *coColor, const wxColour *jtColor);
		void Initial(const wxBitmap& ele, const wxBitmap& brk, const wxBitmap& jt1, const wxBitmap& jt2, const wxBitmap& jt3, const wxBitmap& jt4, int ropeCnt, int sensorCnt, const std::vector<AnalogFlawData>& flaws, const std::vector<AnalogJtData>& jts);
		bool IsInit();
		void DrawChart(float spos, float epos);
		void ReSize(const wxSize& size);
		wxSize GetSize();
		void Show();
		void Hide();
		wxPanel *GetPanel();
		///获取图片，angle可旋转90、-90、180度
		wxBitmap GetBitmap(short angle=0);
		///移动、放大缩小
		// move: 0=不移动，-1=左翻一屏，1=右翻一屏
		// zoom: >0=X轴缩放，1.0=X轴不缩放，>1.0=放大，<1.0=缩小
		void MoveAndZoom(int move, float zoom);

    private:
		void Initial();
        void OnPaint(wxPaintEvent& event);
		void OnMouseWheel(wxMouseEvent& event);
		void OnLeftDown(wxMouseEvent& event);
        void OnLeftUp(wxMouseEvent& event);
        void OnMouseMove(wxMouseEvent& event);

    private:
		wxWindow *m_parent;
		wxPanel *m_panel;
		wxMemoryDC m_dc;
        wxBitmap m_bmp;
		wxSize m_size;
		wxFont m_font;

        bool m_isInit;
		wxBitmap m_bgBmp;
		wxColour m_bgColor;
		wxColour m_coColor;
        wxColour m_jtColor;
		wxBitmap m_eleBmp;
		wxBitmap m_brkBmp;
		wxBitmap m_jtBmp[4];
		int m_ropeCnt;
		int m_sensorCnt;
		std::vector<AnalogFlawData> m_flaws;
		std::vector<AnalogJtData> m_jts;

		///x轴起止值
        float m_spos;
        float m_epos;

		///鼠标左键or右键按下（0=未按下，1=左键按下，2=右键按下）
		short m_leftOrRight;
		///记录鼠标位置
        wxPoint m_xLogicalPos;
        wxPoint m_yLogicalPos;

};

#endif
