#调试信息指定保存文件（默认空则为stdout输出）
debug_save_file debug.txt

dbg_main 0
dbg_web 0
dbg_view 0

#系统类型（0=在线，1=索道，2=猴车，3=电梯，5=皮带）
sys_type 0

#指定语言（0=系统环境，1=中文，2=英文，3=韩文，4=日文）
language 1

#服务器IP地址
iot_server_ip 127.0.0.1
#WebSocket服务端口
web_server_port 9100
#WebSocket连接心跳周期（秒）
web_alive_time 10
#Web接口是否启用非加密传输（要与服务端设置一致）
web_non_encryp 0
#View服务端口
view_server_port 9200
#View连接心跳周期（秒）
view_alive_time 3
#View接口是否加密传输（要与服务端设置一致）
view_encryption 0
#本地数据（原始数据）服务端口
local_server_port 9300

#线程池线程数量
thread_pool_count 3

#DES秘钥
des_key XBNtWQmxVhg=

#DES补码字符
des_patching_char *

#MRT实时曲线屏宽显示长度，单位：米
mrt_onescreen_len 100

#实时曲线显示方式，0=波形图，1=模拟图，0x02=模拟图使用真实钢芯数
curve_display_mode 0

#MRT波形图展示系数
mrt_curve_coeff 2

#实时流帧率
vis_frame_rate 15

#【损伤】过滤，可设置一个门限，过滤小损伤
flaw_filterate 0
#【损伤】过滤，统计率
flaw_stat_filter 30

#【在线】运行图方向，0=横向，1=竖向（深度），2=竖向（高度）
zx_runchart_dir 0
