<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="TCK.W_Client_IoT" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/TCKW_Client_IoT" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Option projectLinkerOptionsRelation="2" />
				<Compiler>
					<Add option="-g" />
					<Add option="-D__WXDEBUG__" />
					<Add directory="$(#wx)/lib/gcc_lib/mswud" />
				</Compiler>
				<ResourceCompiler>
					<Add directory="$(#wx)/lib/gcc_lib/mswud" />
				</ResourceCompiler>
				<Linker>
					<Add library="libwxmsw29ud_xrc.a" />
					<Add library="libwxmsw29ud_aui.a" />
					<Add library="libwxmsw29ud_media.a" />
					<Add library="libwxbase29ud_net.a" />
					<Add library="libwxbase29ud_xml.a" />
					<Add library="libwxmsw29ud_adv.a" />
					<Add library="libwxmsw29ud_html.a" />
					<Add library="libwxmsw29ud_core.a" />
					<Add library="libwxbase29ud.a" />
					<Add library="libwxpngd.a" />
					<Add library="libwxjpegd.a" />
					<Add library="libwxtiffd.a" />
					<Add library="libwxzlibd.a" />
					<Add library="libwxexpatd.a" />
					<Add directory="$(#wx)/lib/gcc_lib" />
				</Linker>
			</Target>
			<Target title="Release">
				<Option output="bin/Release/TCKW_Client_IoT" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Release/" />
				<Option type="0" />
				<Option compiler="gnu_gcc_compiler_x86" />
				<Option projectLinkerOptionsRelation="2" />
				<Compiler>
					<Add option="-O2" />
					<Add directory="$(#wx29)/lib/gcc_lib/mswu" />
					<Add directory="include" />
				</Compiler>
				<ResourceCompiler>
					<Add directory="$(#wx29)/lib/gcc_lib/mswu" />
				</ResourceCompiler>
				<Linker>
					<Add option="-s" />
					<Add library="libwxmsw29u_xrc.a" />
					<Add library="libwxmsw29u_aui.a" />
					<Add library="libwxmsw29u_media.a" />
					<Add library="libwxbase29u_net.a" />
					<Add library="libwxbase29u_xml.a" />
					<Add library="libwxmsw29u_adv.a" />
					<Add library="libwxmsw29u_html.a" />
					<Add library="libwxmsw29u_core.a" />
					<Add library="libwxbase29u.a" />
					<Add library="libwxpng.a" />
					<Add library="libwxjpeg.a" />
					<Add library="libwxtiff.a" />
					<Add library="libwxzlib.a" />
					<Add library="libwxexpat.a" />
					<Add library="dllyb_win" />
					<Add directory="$(#wx29)/lib/gcc_lib" />
					<Add directory="lib" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
			<Add option="-pipe" />
			<Add option="-mthreads" />
			<Add option='[[if (PLATFORM == PLATFORM_MSW &amp;&amp; (GetCompilerFactory().GetCompilerVersionString(_T(&quot;gcc&quot;)) &gt;= _T(&quot;4.0.0&quot;))) print(_T(&quot;-Wno-attributes&quot;));]]' />
			<Add option="-D__GNUWIN32__" />
			<Add option="-D__WXMSW__" />
			<Add option="-DwxUSE_UNICODE" />
			<Add directory="$(#wx29)/include" />
			<Add directory="$(#wx29)/contrib/include" />
		</Compiler>
		<ResourceCompiler>
			<Add directory="$(#wx29)/include" />
		</ResourceCompiler>
		<Linker>
			<Add option="-mthreads" />
			<Add library="libkernel32.a" />
			<Add library="libuser32.a" />
			<Add library="libgdi32.a" />
			<Add library="libwinspool.a" />
			<Add library="libcomdlg32.a" />
			<Add library="libadvapi32.a" />
			<Add library="libshell32.a" />
			<Add library="libole32.a" />
			<Add library="liboleaut32.a" />
			<Add library="libuuid.a" />
			<Add library="libcomctl32.a" />
			<Add library="libwsock32.a" />
			<Add library="libodbc32.a" />
		</Linker>
		<Unit filename="DebugWin.cpp" />
		<Unit filename="DebugWin.h" />
		<Unit filename="MainFrame.cpp" />
		<Unit filename="MainFrame.h" />
		<Unit filename="NoteWin.cpp" />
		<Unit filename="NoteWin.h" />
		<Unit filename="QueryData.cpp" />
		<Unit filename="QueryData.h" />
		<Unit filename="TCK_W_Client_IoTApp.cpp" />
		<Unit filename="TCK_W_Client_IoTApp.h" />
		<Unit filename="TCK_W_Client_IoTMain.cpp" />
		<Unit filename="TCK_W_Client_IoTMain.h" />
		<Unit filename="resource.rc">
			<Option compilerVar="WINDRES" />
		</Unit>
		<Unit filename="tcp_client_view.cpp" />
		<Unit filename="tcp_client_view.h" />
		<Unit filename="tcp_client_web.cpp" />
		<Unit filename="tcp_client_web.h" />
		<Unit filename="wxsmith/TCK_W_Client_IoTdialog.wxs" />
		<Extensions>
			<code_completion />
			<envvars />
			<debugger />
			<wxsmith version="1">
				<gui name="wxWidgets" src="TCK_W_Client_IoTApp.cpp" main="TCK_W_Client_IoTDialog" init_handlers="necessary" language="CPP" />
				<resources>
					<wxDialog wxs="wxsmith/TCK_W_Client_IoTdialog.wxs" src="TCK_W_Client_IoTMain.cpp" hdr="TCK_W_Client_IoTMain.h" fwddecl="0" i18n="1" name="TCK_W_Client_IoTDialog" language="CPP" />
					<wxDialog wxs="wxsmith/DebugWin.wxs" src="DebugWin.cpp" hdr="DebugWin.h" fwddecl="0" i18n="1" name="DebugWin" language="CPP" />
					<wxFrame wxs="wxsmith/MainFrame.wxs" src="MainFrame.cpp" hdr="MainFrame.h" fwddecl="0" i18n="1" name="MainFrame" language="CPP" />
				</resources>
			</wxsmith>
		</Extensions>
	</Project>
</CodeBlocks_project_file>
