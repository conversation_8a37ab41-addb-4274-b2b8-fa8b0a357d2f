#ifndef BELTTEARREPORT_H
#define BELTTEARREPORT_H

//(*Headers(BeltTearReport)
#include <wx/button.h>
#include <wx/dialog.h>
#include <wx/gauge.h>
#include <wx/statbox.h>
#include <wx/stattext.h>
#include <wx/textctrl.h>
//*)
#include <wx/print.h>
#include <string>
#include <vector>
#include "tcp_client_web.h"
#include "tcp_client_srcdata.h"

typedef struct
{
	int fid;
	wxString date;
	float pos;
	float length;
	int width;
	int type;
	std::vector<wxString> fnames;
} TearData;

class BeltTearReport: public wxDialog, public ThreadBase
{
	public:

		BeltTearReport(wxWindow* parent, const char *sn, const char *node, const char *name, const TcpClientWeb::DataEvtReplyRope *rope_info,
					std::vector<TearData>& tearData, TcpClientSrcdata *tcp_srcdata,
					wxWindowID id=wxID_ANY,const wxPoint& pos=wxDefaultPosition,const wxSize& size=wxDefaultSize);
		virtual ~BeltTearReport();

		void TearPhotoProgress(int pec, int fmt, wxString fnm);

		void ThreadFuncReport(void *arg, unsigned int len);

		//(*Declarations(BeltTearReport)
		wxButton* Button1;
		wxGauge* Gauge1;
		wxStaticBox* StaticBox1;
		wxStaticBox* StaticBox2;
		wxStaticText* StaticText10;
		wxStaticText* StaticText11;
		wxStaticText* StaticText12;
		wxStaticText* StaticText13;
		wxStaticText* StaticText14;
		wxStaticText* StaticText15;
		wxStaticText* StaticText16;
		wxStaticText* StaticText17;
		wxStaticText* StaticText18;
		wxStaticText* StaticText1;
		wxStaticText* StaticText23;
		wxStaticText* StaticText24;
		wxStaticText* StaticText25;
		wxStaticText* StaticText2;
		wxStaticText* StaticText3;
		wxStaticText* StaticText4;
		wxStaticText* StaticText5;
		wxStaticText* StaticText7;
		wxStaticText* StaticText8;
		wxStaticText* StaticText9;
		wxTextCtrl* TextCtrl10;
		wxTextCtrl* TextCtrl11;
		wxTextCtrl* TextCtrl12;
		wxTextCtrl* TextCtrl13;
		wxTextCtrl* TextCtrl14;
		wxTextCtrl* TextCtrl15;
		wxTextCtrl* TextCtrl17;
		wxTextCtrl* TextCtrl18;
		wxTextCtrl* TextCtrl1;
		wxTextCtrl* TextCtrl2;
		wxTextCtrl* TextCtrl3;
		wxTextCtrl* TextCtrl4;
		wxTextCtrl* TextCtrl5;
		wxTextCtrl* TextCtrl6;
		wxTextCtrl* TextCtrl7;
		wxTextCtrl* TextCtrl8;
		wxTextCtrl* TextCtrl9;
		//*)

	protected:

		void OnReportOK(wxCommandEvent& event);

		//(*Identifiers(BeltTearReport)
		static const long ID_STATICBOX2;
		static const long ID_STATICBOX1;
		static const long ID_STATICTEXT1;
		static const long ID_TEXTCTRL1;
		static const long ID_STATICTEXT2;
		static const long ID_TEXTCTRL2;
		static const long ID_STATICTEXT3;
		static const long ID_TEXTCTRL3;
		static const long ID_STATICTEXT4;
		static const long ID_TEXTCTRL4;
		static const long ID_STATICTEXT5;
		static const long ID_TEXTCTRL5;
		static const long ID_STATICTEXT7;
		static const long ID_TEXTCTRL6;
		static const long ID_STATICTEXT8;
		static const long ID_TEXTCTRL7;
		static const long ID_STATICTEXT9;
		static const long ID_TEXTCTRL8;
		static const long ID_STATICTEXT10;
		static const long ID_STATICTEXT11;
		static const long ID_TEXTCTRL9;
		static const long ID_STATICTEXT12;
		static const long ID_STATICTEXT13;
		static const long ID_TEXTCTRL10;
		static const long ID_STATICTEXT14;
		static const long ID_STATICTEXT15;
		static const long ID_TEXTCTRL11;
		static const long ID_STATICTEXT16;
		static const long ID_TEXTCTRL12;
		static const long ID_STATICTEXT17;
		static const long ID_TEXTCTRL13;
		static const long ID_STATICTEXT18;
		static const long ID_TEXTCTRL14;
		static const long ID_TEXTCTRL15;
		static const long ID_GAUGE1;
		static const long ID_BUTTON1;
		static const long ID_STATICTEXT23;
		static const long ID_TEXTCTRL17;
		static const long ID_STATICTEXT24;
		static const long ID_TEXTCTRL18;
		static const long ID_STATICTEXT25;
		//*)

	private:

		wxWindow *m_parent;
		std::string m_sn;
		std::string m_node;
		TcpClientWeb::DataEvtReplyRope m_rope_info;
		std::vector<TearData> m_tearData;
		int m_tdt_idx;

		TcpClientSrcdata *m_tcp_srcdata;

		//(*Handlers(BeltTearReport)
		void OnButton1Click(wxCommandEvent& event);
		//*)

		DECLARE_EVENT_TABLE()

		std::vector<wxBitmap> m_previewBmp;
		bool m_isPrintPreviewExist;
        wxPrintData m_printData;
        wxPrintDialogData m_printDialogData;
        wxPageSetupDialogData m_pageSetupDialogData;
        unsigned int m_totalPages;

	public:
		friend class BeltTearPrintout;
};


class BeltTearPrintout : public wxPrintout
{
    public:
        BeltTearPrintout(BeltTearReport *parent);
        ~BeltTearPrintout();
        bool OnPrintPage(int pageNum);
        bool HasPage(int pageNum);
        void GetPageInfo(int *minPage, int *maxPage, int *selPageFrom, int *selPageTo);
    private:
        BeltTearReport *m_parent;
};

#endif
