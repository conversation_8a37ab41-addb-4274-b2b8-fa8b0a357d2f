#ifndef TRANSSTATICBMP_H
#define TRANSSTATICBMP_H

#include <wx/window.h>
#include <wx/dcmemory.h>
#include <wx/bitmap.h>
#include <wx/timer.h>

class TransStaticBmp : public wxWindow
{
    public:
        TransStaticBmp(wxWindow *parent, const wxPoint &pos, const wxSize &size, const wxBitmap &bmp, wxBitmap *pbgBmp=NULL);
        TransStaticBmp(wxWindow *parent, const wxPoint &pos, const wxSize &size, int appoint, wxBitmap *pbgBmp=NULL);
        virtual ~TransStaticBmp();
        void SetBitmap(const wxBitmap &bmp);
        void SetAppoint(int appoint, bool isResize=false);
        void SetPosition( const wxPoint &  pt );
        void SetSize( const wxSize &  size );
    protected:
        void OnPaint(wxPaintEvent &event);
        void OnEraseBackground(wxEraseEvent &event);
    private:
        wxWindow *m_parent;
        wxBitmap *m_pbgBmp;
        wxBitmap m_srcBmp;
        wxBitmap m_bmp;
        wxSize m_size;
        wxPoint m_pos;
        int m_appoint;
        wxBitmap m_bmpList[6];
        int m_bmp_idx;
        wxTimer m_timer;
        void OnTimerTrigger(wxTimerEvent& event);
};

#endif // TRANSSTATICBMP_H
